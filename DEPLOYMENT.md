# Alpago Next.js Deployment Guide

## Overview
This Next.js application is configured to run with PM2 in cluster mode for production deployment.

## Quick Start
```bash
# Run the deployment script
./deploy.sh
```

## Manual Deployment Steps

### 1. Install Dependencies
```bash
npm install
```

### 2. Build the Application
```bash
npm run build
```

### 3. Start with PM2 (Production)
```bash
npm run pm2:start -- --env production
```

## PM2 Management Commands

### Status and Monitoring
```bash
# Check application status
npx pm2 status

# View logs
npm run pm2:logs

# Monitor in real-time
npx pm2 monit
```

### Process Management
```bash
# Restart application
npm run pm2:restart

# Stop application
npm run pm2:stop

# Delete application from PM2
npm run pm2:delete

# Reload application (zero-downtime)
npx pm2 reload alpago
```

### Configuration
- **Application Name**: alpago
- **Port**: 3006 (production)
- **Instances**: max (uses all CPU cores)
- **Mode**: cluster
- **Environment**: production

### Log Files
- Error logs: `./logs/err.log`
- Output logs: `./logs/out.log`
- Combined logs: `./logs/combined.log`

### Environment Variables
- `APPLICATION_ENV`: production
- `APPLICATION_PORT`: 3006

## Auto-start on System Boot
To enable auto-start on system boot, run the command provided by:
```bash
npx pm2 startup
```

Then save the current process list:
```bash
npx pm2 save
```

## Troubleshooting

### Check if application is running
```bash
curl -I http://localhost:3006
```

### View detailed logs
```bash
npx pm2 logs alpago --lines 100
```

### Restart if issues occur
```bash
npm run pm2:restart
```

## Domain Configuration
The application is configured to work with: https://alpago-dev-new.e8demo.com

Make sure your reverse proxy (nginx/apache) is configured to forward requests to `http://localhost:3006`.
