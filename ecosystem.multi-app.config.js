// Multi-Application PM2 Configuration Template
// Copy this file to each application directory and customize

module.exports = {
  apps: [
    // Example: Alpago Application
    {
      name: 'alpago',
      script: 'server.js',
      cwd: '/path/to/alpago-webapp',
      instances: process.env.PM2_INSTANCES || 2,
      exec_mode: 'cluster',
      env: {
        APPLICATION_ENV: 'development',
        APPLICATION_PORT: 3005,
        NODE_ENV: 'development'
      },
      env_production: {
        APPLICATION_ENV: 'production',
        APPLICATION_PORT: 3006,
        NODE_ENV: 'production'
      },
      max_memory_restart: '512M',
      error_file: '/path/to/alpago-webapp/logs/err.log',
      out_file: '/path/to/alpago-webapp/logs/out.log',
      log_file: '/path/to/alpago-webapp/logs/combined.log',
      time: true,
      min_uptime: '10s',
      max_restarts: 5,
      node_args: '--max-old-space-size=512',
      namespace: 'alpago-app'
    },
    
    // Example: API Application
    {
      name: 'api-server',
      script: 'app.js',
      cwd: '/path/to/api-server',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        PORT: 4000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 4001
      },
      max_memory_restart: '256M',
      error_file: '/path/to/api-server/logs/err.log',
      out_file: '/path/to/api-server/logs/out.log',
      log_file: '/path/to/api-server/logs/combined.log',
      time: true,
      min_uptime: '10s',
      max_restarts: 5,
      node_args: '--max-old-space-size=256',
      namespace: 'api-app'
    },
    
    // Example: Admin Dashboard
    {
      name: 'admin-dashboard',
      script: 'npm',
      args: 'start',
      cwd: '/path/to/admin-dashboard',
      instances: 1, // Admin usually needs fewer instances
      exec_mode: 'fork', // Can use fork mode for lighter applications
      env: {
        NODE_ENV: 'development',
        PORT: 5000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5001
      },
      max_memory_restart: '256M',
      error_file: '/path/to/admin-dashboard/logs/err.log',
      out_file: '/path/to/admin-dashboard/logs/out.log',
      log_file: '/path/to/admin-dashboard/logs/combined.log',
      time: true,
      min_uptime: '10s',
      max_restarts: 5,
      node_args: '--max-old-space-size=256',
      namespace: 'admin-app'
    },
    
    // Example: Background Worker
    {
      name: 'worker-service',
      script: 'worker.js',
      cwd: '/path/to/worker-service',
      instances: 1, // Workers usually run single instance
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production'
      },
      max_memory_restart: '128M',
      error_file: '/path/to/worker-service/logs/err.log',
      out_file: '/path/to/worker-service/logs/out.log',
      log_file: '/path/to/worker-service/logs/combined.log',
      time: true,
      min_uptime: '10s',
      max_restarts: 5,
      cron_restart: '0 2 * * *', // Restart daily at 2 AM
      node_args: '--max-old-space-size=128',
      namespace: 'worker-app'
    }
  ]
};
