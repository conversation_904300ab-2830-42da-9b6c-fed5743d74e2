{"name": "alpago", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "pm2:start": "npx pm2 start ecosystem.config.js", "pm2:stop": "npx pm2 stop alpago", "pm2:restart": "npx pm2 restart alpago", "pm2:delete": "npx pm2 delete alpago", "pm2:logs": "npx pm2 logs alpago"}, "dependencies": {"@gsap/react": "^2.1.1", "@studio-freight/lenis": "^1.0.42", "aos": "^2.3.4", "axios": "^1.7.9", "dashjs": "^5.0.2", "framer-motion": "^11.18.1", "gsap": "^3.12.7", "html-react-parser": "^5.2.2", "locomotive-scroll": "^4.1.4", "next": "14.2.5", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-icomoon": "^2.5.7", "react-icons": "^5.5.0", "react-intersection-observer": "^9.15.1", "react-responsive": "^10.0.0", "react-router-dom": "^7.1.3", "react-select": "^5.10.1", "sass": "^1.83.1", "split-type": "^0.3.4", "sweetalert2": "^11.17.2", "swiper": "^11.2.0", "use-debounce": "^10.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.4", "pm2": "^6.0.8"}}