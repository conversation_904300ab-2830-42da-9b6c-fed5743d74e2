{"name": "dashjs", "version": "5.0.2", "description": "A reference client implementation for the playback of MPEG DASH via Javascript and compliant browsers.", "author": "Dash Industry Forum", "license": "BSD-3-<PERSON><PERSON>", "exports": {".": {"types": "./index.d.ts", "import": "./dist/modern/esm/dash.all.min.js", "default": "./dist/modern/esm/dash.all.min.js", "browser": "./dist/modern/umd/dash.all.min.js", "script": "./dist/modern/umd/dash.all.min.js", "require": "./dist/modern/umd/dash.all.min.js"}, "./mss": {"types": "./index.d.ts", "import": "./dist/modern/esm/dash.mss.min.js", "default": "./dist/modern/esm/dash.mss.min.js", "browser": "./dist/modern/umd/dash.mss.min.js", "script": "./dist/modern/umd/dash.mss.min.js", "require": "./dist/modern/umd/dash.mss.min.js"}}, "type": "module", "scripts": {"build": "npm run webpack-build-modern && npm run webpack-build-legacy", "build-legacy": "npm run prebuild && npm run webpack-build-legacy", "build-modern": "npm run prebuild && npm run webpack-build-modern", "dev": "tsc && webpack --config build/webpack/modern/webpack.modern.dev.cjs --mode development --watch --progress", "doc": "jsdoc -c build/jsdoc/jsdoc_conf.json -d docs/jsdoc", "lint": "eslint \"src/**/*.js\" \"test/unit/mocks/*.js\" \"test/unit/test/**/*.js\"", "prebuild": "rimraf dist && tsc && npm run test && npm run lint", "prepack": "npm run build", "prepare": "node githook.cjs", "start": "webpack serve --config build/webpack/modern/webpack.modern.dev.cjs", "test": "karma start test/unit/config/karma.unit.conf.cjs", "test-functional": "karma start test/functional/config/karma.functional.conf.cjs --configfile=local --streamsfile=smoke", "webpack-build-legacy": "webpack --config build/webpack/legacy/webpack.legacy.prod.cjs", "webpack-build-modern": "webpack --config build/webpack/modern/webpack.modern.prod.cjs"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.24.1", "@babel/plugin-transform-parameters": "^7.25.9", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/preset-env": "^7.24.4", "@chiragrupani/karma-chromium-edge-launcher": "^2.4.1", "@eslint/js": "^9.13.0", "babel-loader": "^9.1.3", "babel-plugin-istanbul": "^7.0.0", "chai": "^4.4.1", "chai-spies": "^1.1.0", "clean-jsdoc-theme": "^4.2.17", "core-js": "^3.39.0", "eslint": "^9.13.0", "eslint-webpack-plugin": "^4.2.0", "globals": "^15.11.0", "jsdoc": "^4.0.3", "karma": "^6.4.1", "karma-browserstack-launcher": "^1.6.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.1", "karma-coverage": "^2.2.1", "karma-firefox-launcher": "^2.1.3", "karma-htmlfile-reporter": "^0.3.8", "karma-junit-reporter": "^2.0.1", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-safarinative-launcher": "^1.1.0", "karma-webdriver-launcher": "^1.0.8", "karma-webpack": "^5.0.0", "mocha": "^10.1.0", "rimraf": "^5.0.5", "sinon": "^17.0.1", "stream-browserify": "^3.0.0", "string-replace-loader": "^3.1.0", "timers-browserify": "^2.0.12", "typescript": "^5.4.5", "webpack": "5.94.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-merge": "^5.10.0", "yargs": "^17.7.2"}, "dependencies": {"@svta/common-media-library": "^0.11.0", "bcp-47-match": "^2.0.3", "bcp-47-normalize": "^2.3.0", "codem-isoboxer": "0.3.10", "fast-deep-equal": "3.1.3", "html-entities": "^2.5.2", "imsc": "^1.1.5", "localforage": "^1.10.0", "path-browserify": "^1.0.1", "ua-parser-js": "^1.0.37"}, "repository": {"type": "git", "url": "https://github.com/Dash-Industry-Forum/dash.js.git"}, "files": ["githook.cjs", "index.d.ts", "dist", "contrib/akamai", "contrib/videojs-vtt.js/vtt.min.js"], "keywords": ["DASH", "DASH-IF", "MSE", "EME", "Smooth Streaming"]}