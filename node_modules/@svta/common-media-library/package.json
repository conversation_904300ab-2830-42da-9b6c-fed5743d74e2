{"name": "@svta/common-media-library", "version": "0.11.0", "license": "Apache-2.0", "homepage": "https://github.com/streaming-video-technology-alliance/common-media-library", "authors": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/streaming-video-technology-alliance/common-media-library.git"}, "bugs": {"url": "https://github.com/streaming-video-technology-alliance/common-media-library/issues"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org", "provenance": true, "scope": "svta"}, "files": ["dist/**/*", "NOTICE"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/cjs/index.js", "default": "./dist/index.js"}, "./*.js": {"import": "./dist/*.js", "require": "./dist/cjs/*.js", "default": "./dist/*.js"}, "./*": {"types": "./dist/*.d.ts", "import": "./dist/*.js", "require": "./dist/cjs/*.js", "default": "./dist/*.js"}}, "typesVersions": {"*": {"*": ["dist/*", "dist/cjs/*"]}}, "keywords": ["CTA-5004", "Common Media Client Data", "CMCD", "CTA-5006", "Common Media Server Data", "CMSD", "RFC8941", "Structured Field Values", "ID3", "Common Media Request", "Common Media Response", "CTA-608", "CEA-608", "EIA-608"], "scripts": {"build": "npm run build:esm && npm run build:cjs", "postbuild": "api-extractor run --local", "build:esm": "tsx ../scripts/build.mts", "build:cjs": "tsc --module commonjs --outDir dist/cjs", "test": "tsx --test ./test/**/*.test.ts", "prepublishOnly": "npm run build"}}