#!/bin/bash

# Multi-Application PM2 Management Script
# Usage: ./multi-app-manager.sh [command] [app-name]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to show system resources
show_resources() {
    print_header "SYSTEM RESOURCES"
    
    # CPU cores
    CPU_CORES=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo "Unknown")
    echo "CPU Cores: $CPU_CORES"
    
    # Memory
    if command -v free >/dev/null 2>&1; then
        free -h
    elif command -v vm_stat >/dev/null 2>&1; then
        echo "Memory Info (macOS):"
        vm_stat | head -4
    fi
    
    echo ""
}

# Function to show all PM2 applications
show_all_apps() {
    print_header "ALL PM2 APPLICATIONS"
    npx pm2 list
    echo ""
    
    print_header "RESOURCE USAGE BY APPLICATION"
    npx pm2 show all 2>/dev/null | grep -E "(App name|memory|cpu)" || echo "No detailed stats available"
    echo ""
}

# Function to show port usage
show_ports() {
    print_header "PORT USAGE"
    echo "Checking common application ports..."
    
    for port in 3000 3001 3002 3003 3004 3005 3006 3007 3008 3009 8000 8080 8081; do
        if lsof -i :$port >/dev/null 2>&1; then
            echo "Port $port: $(lsof -i :$port | tail -n +2 | awk '{print $1}' | head -1) (OCCUPIED)"
        else
            echo "Port $port: Available"
        fi
    done
    echo ""
}

# Function to restart with resource optimization
restart_optimized() {
    local app_name=${1:-"alpago"}
    
    print_header "OPTIMIZED RESTART FOR $app_name"
    
    # Stop the application
    print_status "Stopping $app_name..."
    npx pm2 stop $app_name
    
    # Set optimized instances based on available resources
    local total_apps=$(npx pm2 list | grep -c "online\|stopped\|errored" | head -1)
    local recommended_instances=$((CPU_CORES / (total_apps + 1)))
    
    if [ $recommended_instances -lt 1 ]; then
        recommended_instances=1
    elif [ $recommended_instances -gt 4 ]; then
        recommended_instances=4
    fi
    
    print_status "Recommended instances for $app_name: $recommended_instances"
    
    # Set environment variable for instances
    export PM2_INSTANCES=$recommended_instances
    
    # Restart with new configuration
    print_status "Starting $app_name with $recommended_instances instances..."
    npm run pm2:start -- --env production
    
    print_status "Restart completed!"
}

# Main script logic
case "$1" in
    "status"|"list")
        show_resources
        show_all_apps
        show_ports
        ;;
    "restart")
        restart_optimized $2
        ;;
    "optimize")
        print_header "OPTIMIZING ALL APPLICATIONS"
        print_warning "This will restart all applications with optimized settings"
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # Get list of all apps
            apps=$(npx pm2 list | grep -E "online|stopped" | awk '{print $4}' | sort -u)
            for app in $apps; do
                if [ "$app" != "│" ] && [ "$app" != "" ]; then
                    restart_optimized $app
                fi
            done
        fi
        ;;
    "monitor")
        print_header "REAL-TIME MONITORING"
        npx pm2 monit
        ;;
    "logs")
        app_name=${2:-"all"}
        print_header "LOGS FOR $app_name"
        npx pm2 logs $app_name
        ;;
    "help"|*)
        echo "Multi-Application PM2 Manager"
        echo ""
        echo "Usage: $0 [command] [app-name]"
        echo ""
        echo "Commands:"
        echo "  status     - Show system resources, all apps, and port usage"
        echo "  restart    - Restart specific app with optimized settings"
        echo "  optimize   - Optimize all applications for multi-app environment"
        echo "  monitor    - Open real-time monitoring dashboard"
        echo "  logs       - Show logs for specific app (default: all)"
        echo "  help       - Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 status"
        echo "  $0 restart alpago"
        echo "  $0 logs alpago"
        echo "  $0 optimize"
        ;;
esac
