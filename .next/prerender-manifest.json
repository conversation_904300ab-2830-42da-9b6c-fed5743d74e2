{"version": 4, "routes": {"/favicon.ico": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "image/x-icon", "x-next-cache-tags": "_N_T_/layout,_N_T_/favicon.ico/layout,_N_T_/favicon.ico/route,_N_T_/favicon.ico"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favicon.ico", "dataRoute": null}, "/sitemap_index.xml": {"initialHeaders": {"cache-control": "s-maxage=86400, stale-while-revalidate", "content-type": "application/xml", "x-next-cache-tags": "_N_T_/layout,_N_T_/sitemap_index.xml/layout,_N_T_/sitemap_index.xml/route,_N_T_/sitemap_index.xml"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/sitemap_index.xml", "dataRoute": null}, "/thank-you": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/thank-you", "dataRoute": "/thank-you.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "4d1d20d8af320236ca17ebb19ce833fa", "previewModeSigningKey": "d320de2d7d5d7cbeefe200a49cd940208f7d05e3f7d59e7727b5fa57c577f1b9", "previewModeEncryptionKey": "499bdcbab2216b22cc7927802370c3227539b329c8fe362a83e98c2c6b2781fc"}}