"use strict";exports.id=8595,exports.ids=[8595],exports.modules={98595:(e,a,r)=>{r.r(a),r.d(a,{default:()=>p});var i=r(19510);r(71159);var l=r(58238),t=r.n(l),s=r(68570);let o=(0,s.createProxy)(String.raw`/Users/<USER>/MyFolder/alpago-webapp/components/overlapImageText/OverlapImageText.js`),{__esModule:n,$$typeof:m}=o;o.default;let g=(0,s.createProxy)(String.raw`/Users/<USER>/MyFolder/alpago-webapp/components/overlapImageText/OverlapImageText.js#default`),p=({content:e})=>i.jsx("section",{className:t().imageoverlap,children:i.jsx("div",{className:"container",children:i.jsx(g,{image1:e?.main_image?.full_url,image2:e?.secondary_image?.full_url,imageAlt1:e?.main_image?.alt,imageAlt2:e?.secondary_image?.alt,title:e?.heading,description:e?.description})})})}};