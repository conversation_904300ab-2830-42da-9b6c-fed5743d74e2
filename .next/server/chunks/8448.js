"use strict";exports.id=8448,exports.ids=[8448],exports.modules={78448:(e,s,t)=>{t.r(s),t.d(s,{default:()=>n});var a=t(19510);t(71159);var o=t(6659),l=t(55782);let c={heading_with_border_block:(0,l.default)(()=>t.e(6038).then(t.bind(t,16038)).catch(()=>a.jsx(a.Fragment,{})),{loadableGenerated:{modules:["components/templates/processTemplate/processTemplate.js -> @/components/templates/processTemplate/sections/headingBorderBlock"]},ssr:!1}),image_text_combo_block:(0,l.default)(()=>t.e(9538).then(t.bind(t,89538)).catch(()=>a.jsx(a.Fragment,{})),{loadableGenerated:{modules:["components/templates/processTemplate/processTemplate.js -> @/components/templates/processTemplate/sections/imageTextComboBlock"]},ssr:!1}),text_image_card_list_with_border_block:(0,l.default)(()=>t.e(9565).then(t.bind(t,99565)).catch(()=>a.jsx(a.Fragment,{})),{loadableGenerated:{modules:["components/templates/processTemplate/processTemplate.js -> @/components/templates/processTemplate/sections/imageCardBlock"]},ssr:!1}),project_list_block:(0,l.default)(()=>t.e(4856).then(t.bind(t,4856)).catch(()=>a.jsx(a.Fragment,{})),{loadableGenerated:{modules:["components/templates/processTemplate/processTemplate.js -> @/components/templates/processTemplate/sections/projectListBlock"]},ssr:!1})},n=async({pageData:e})=>(0,a.jsxs)("div",{children:[a.jsx("div",{className:"pageBnnaer_title",children:a.jsx("div",{className:"container",children:a.jsx("h1",{children:"Process"})})}),a.jsx(o.bu,{componentList:c,pageData:e})]})}};