"use strict";exports.id=2061,exports.ids=[2061],exports.modules={52061:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var a=s(19510);s(71159);var n=s(6659),l=s(55782);let o={internal_blog_page:(0,l.default)(()=>s.e(9485).then(s.bind(s,19485)).catch(()=>a.jsx(a.Fragment,{})),{loadableGenerated:{modules:["components/templates/newsTemplate/newsTemplate.js -> @/components/templates/newsTemplate/sections/internalBlogPage"]},ssr:!0}),contact_us_full_image_block:(0,l.default)(()=>s.e(6410).then(s.bind(s,56410)).catch(()=>a.jsx(a.Fragment,{})),{loadableGenerated:{modules:["components/templates/newsTemplate/newsTemplate.js -> @/components/templates/workTemplate/sections/contactUsFullImageBlock"]},ssr:!0})},c=async({pageData:e})=>(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"pageBnnaer_title",children:a.jsx("div",{className:"container",children:a.jsx("h1",{children:e?.title})})}),a.jsx(n.bu,{componentList:o,pageData:e})]})}};