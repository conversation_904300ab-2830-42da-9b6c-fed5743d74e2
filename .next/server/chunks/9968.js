"use strict";exports.id=9968,exports.ids=[9968],exports.modules={19968:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var l=a(19510);a(71159);var o=a(6659),s=a(55782),n=a(34981);let m={description_and_banner_image_block:(0,s.default)(()=>a.e(3161).then(a.bind(a,3161)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailTemplate/workDetailTemplate.js -> @/components/templates/workDetailTemplate/sections/headingBlock"]},ssr:!1}),project_title_with_swiper_image_block:(0,s.default)(()=>a.e(2138).then(a.bind(a,32138)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailTemplate/workDetailTemplate.js -> @/components/templates/workDetailTemplate/sections/projectStatusBlock"]},ssr:!1}),two_column_content_with_image_block:(0,s.default)(()=>a.e(8036).then(a.bind(a,8036)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailTemplate/workDetailTemplate.js -> @/components/templates/workDetailTemplate/sections/galleryBlock"]},ssr:!1}),two_column_heading_description_block:(0,s.default)(()=>a.e(7965).then(a.bind(a,17965)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailTemplate/workDetailTemplate.js -> @/components/templates/workDetailTemplate/sections/fullImageWithDescBlock"]},ssr:!1}),work_section_full_image_block:(0,s.default)(()=>a.e(7552).then(a.bind(a,47552)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailTemplate/workDetailTemplate.js -> @/components/templates/workDetailTemplate/sections/workSectionFullImageBlock"]},ssr:!1}),project_list_block:(0,s.default)(()=>a.e(4989).then(a.bind(a,14989)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailTemplate/workDetailTemplate.js -> @/components/templates/workDetailTemplate/sections/projectSwiperBlock"]},ssr:!1}),contact_us_full_image_block:(0,s.default)(()=>a.e(7450).then(a.bind(a,87450)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailTemplate/workDetailTemplate.js -> @/components/templates/workDetailTemplate/sections/contactUsFullImageBlock"]},ssr:!1})},c=async({pageData:e,breadCrumbs:t})=>(0,l.jsxs)("div",{children:[l.jsx("div",{className:"pageBnnaer_title banner_title_with_breadcrumb",children:(0,l.jsxs)("div",{className:"container",children:[t&&l.jsx(n.Z,{crumbs:t}),l.jsx("h1",{children:e?.title})]})}),l.jsx(o.bu,{componentList:m,pageData:e})]})}};