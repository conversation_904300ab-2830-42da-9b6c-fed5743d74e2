exports.id=3586,exports.ids=[3586],exports.modules={58238:e=>{e.exports={project_heading:"WorkDetail3_project_heading__EfX6d",sec_title:"WorkDetail3_sec_title__fXbfl",project_detail:"WorkDetail3_project_detail__rxxDB",detail_wrap:"WorkDetail3_detail_wrap__fzTDi",image:"WorkDetail3_image__QLLgC",content:"WorkDetail3_content__laReR",title:"WorkDetail3_title__Ani56",description:"WorkDetail3_description__zX50G",item:"WorkDetail3_item__YHyw4",item_title:"WorkDetail3_item_title__Wfcdr",imageoverlap:"WorkDetail3_imageoverlap__edMqT",imagegrid:"WorkDetail3_imagegrid__7e_aj",imagetext_wrap:"WorkDetail3_imagetext_wrap__VthV0",wall_piece:"WorkDetail3_wall_piece__UPuVm"}},33586:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var o=a(19510);a(71159);var l=a(6659),i=a(55782),r=a(58238),n=a.n(r),s=a(34981);let _={full_length_description_block:(0,i.default)(()=>a.e(5448).then(a.bind(a,65448)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailPrivateInfo/workDetailPrivateInfo.js -> @/components/templates/workDetailPrivateInfo/sections/headingBlock"]},ssr:!1}),left_image_project_details_block:(0,i.default)(()=>a.e(8432).then(a.bind(a,78432)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailPrivateInfo/workDetailPrivateInfo.js -> @/components/templates/workDetailPrivateInfo/sections/leftImageProjectDetailsBlock"]},ssr:!1}),right_align_overlayed_image_block:(0,i.default)(()=>a.e(8595).then(a.bind(a,98595)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailPrivateInfo/workDetailPrivateInfo.js -> @/components/templates/workDetailPrivateInfo/sections/textImageOverlayBlock"]},ssr:!1}),multiple_image_above_two_coulmn_description:(0,i.default)(()=>a.e(6383).then(a.bind(a,86383)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailPrivateInfo/workDetailPrivateInfo.js -> @/components/templates/workDetailPrivateInfo/sections/imageTextBlock"]},ssr:!1}),right_align_image_text_block:(0,i.default)(()=>a.e(8274).then(a.bind(a,8274)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailPrivateInfo/workDetailPrivateInfo.js -> @/components/templates/workDetailPrivateInfo/sections/leftTextImageBlock"]},ssr:!1}),project_list_block:(0,i.default)(()=>a.e(4989).then(a.bind(a,14989)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailPrivateInfo/workDetailPrivateInfo.js -> @/components/templates/workDetailTemplate/sections/projectSwiperBlock"]},ssr:!1}),contact_us_full_image_block:(0,i.default)(()=>a.e(7450).then(a.bind(a,87450)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/workDetailPrivateInfo/workDetailPrivateInfo.js -> @/components/templates/workDetailTemplate/sections/contactUsFullImageBlock"]},ssr:!1})},c=async({pageData:e,breadCrumbs:t})=>(0,o.jsxs)("main",{className:n().work_detail3,children:[o.jsx("div",{className:"pageBnnaer_title banner_title_with_breadcrumb",children:(0,o.jsxs)("div",{className:"container",children:[t&&o.jsx(s.Z,{crumbs:t}),o.jsx("h1",{children:e?.title})]})}),o.jsx(l.bu,{componentList:_,pageData:e})]})}};