"use strict";exports.id=9595,exports.ids=[9595],exports.modules={79595:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var o=s(19510);s(71159);var l=s(6659),a=s(55782);let p={heading_with_border_block:(0,a.default)(()=>s.e(4051).then(s.bind(s,24051)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/philosophyTemplate/philosophyTemplate.js -> @/components/templates/philosophyTemplate/sections/headingBorderBlock"]},ssr:!1}),image_block:(0,a.default)(()=>s.e(937).then(s.bind(s,10937)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/philosophyTemplate/philosophyTemplate.js -> @/components/templates/philosophyTemplate/sections/imageBlock"]},ssr:!1}),two_column_heading_description_block:(0,a.default)(()=>s.e(6581).then(s.bind(s,56581)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/philosophyTemplate/philosophyTemplate.js -> @/components/templates/philosophyTemplate/sections/twoColHeading"]},ssr:!1}),text_card_list_block:(0,a.default)(()=>Promise.all([s.e(6396),s.e(324)]).then(s.bind(s,50324)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/philosophyTemplate/philosophyTemplate.js -> @/components/templates/philosophyTemplate/sections/textCardBlock"]},ssr:!1}),inverted_semi_circle_block:(0,a.default)(()=>s.e(1534).then(s.bind(s,11534)).catch(()=>o.jsx(o.Fragment,{})),{loadableGenerated:{modules:["components/templates/philosophyTemplate/philosophyTemplate.js -> @/components/templates/philosophyTemplate/sections/invertedCircleBlock"]},ssr:!1})},n=async({pageData:e})=>(0,o.jsxs)("div",{children:[o.jsx("div",{className:"pageBnnaer_title",children:o.jsx("div",{className:"container",children:o.jsx("h1",{children:"Philosophy"})})}),o.jsx(l.bu,{componentList:p,pageData:e})]})}};