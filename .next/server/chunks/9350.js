"use strict";exports.id=9350,exports.ids=[9350],exports.modules={49350:(e,o,l)=>{l.r(o),l.d(o,{default:()=>g});var s=l(19510);l(71159);var a=l(3326),r=l.n(a),i=l(68570);let t=(0,i.createProxy)(String.raw`/Users/<USER>/MyFolder/alpago-webapp/components/Philosophy/Philosophy.js`),{__esModule:n,$$typeof:d}=t;t.default;let p=(0,i.createProxy)(String.raw`/Users/<USER>/MyFolder/alpago-webapp/components/Philosophy/Philosophy.js#default`),g=({content:e})=>(0,s.jsxs)("div",{className:r().philo_sec,children:[s.jsx("h3",{className:r().mob_title,dangerouslySetInnerHTML:{__html:e?.heading}}),s.jsx(p,{title:e?.heading,points:e?.points,links:e?.links,background:e?.background_image?.full_url,backgroundAlt:e?.background_image?.alt,mobileBg:e?.mob_background_image?.full_url})]})}};