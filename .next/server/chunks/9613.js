"use strict";exports.id=9613,exports.ids=[9613],exports.modules={29613:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var s=a(19510);a(71159);var l=a(6659),n=a(55782);let m={internal_blog_page:(0,n.default)(()=>a.e(1022).then(a.bind(a,91022)).catch(()=>s.jsx(s.Fragment,{})),{loadableGenerated:{modules:["components/templates/mediaTemplate/mediaTemplate.js -> @/components/templates/mediaTemplate/sections/internalBlogPage"]},ssr:!1}),image_vedio_block:(0,n.default)(()=>a.e(9013).then(a.bind(a,79013)).catch(()=>s.jsx(s.Fragment,{})),{loadableGenerated:{modules:["components/templates/mediaTemplate/mediaTemplate.js -> @/components/templates/mediaTemplate/sections/imageVideoBlock"]},ssr:!1}),contact_us_full_image_block:(0,n.default)(()=>a.e(6410).then(a.bind(a,56410)).catch(()=>s.jsx(s.Fragment,{})),{loadableGenerated:{modules:["components/templates/mediaTemplate/mediaTemplate.js -> @/components/templates/workTemplate/sections/contactUsFullImageBlock"]},ssr:!1})},o=async({pageData:e})=>(0,s.jsxs)("div",{children:[s.jsx("div",{className:"pageBnnaer_title",children:s.jsx("div",{className:"container",children:s.jsx("h1",{children:e?.title})})}),s.jsx(l.bu,{componentList:m,pageData:e})]})}};