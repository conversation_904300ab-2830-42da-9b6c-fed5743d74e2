exports.id=4692,exports.ids=[4692],exports.modules={32027:e=>{e.exports={comp_detail:"CompDetail_comp_detail__YtKEk",we_aim:"CompDetail_we_aim__kopqS",aim_wrap:"CompDetail_aim_wrap__C2Rkm",image:"CompDetail_image__PQElI",content:"CompDetail_content__bzCkS",title:"CompDetail_title__9c_v4"}},94692:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var l=a(19510);a(71159);var m=a(6659),c=a(55782),o=a(32027),s=a.n(o),n=a(34981);let i={base_image_content_block:(0,c.default)(()=>a.e(522).then(a.bind(a,90522)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/competencyDetailTemplate/competencyDetailTemplate.js -> @/components/templates/competencyDetailTemplate/sections/headingBlock"]},ssr:!0}),image_projected_with_border_block:(0,c.default)(()=>a.e(5383).then(a.bind(a,75383)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/competencyDetailTemplate/competencyDetailTemplate.js -> @/components/templates/competencyDetailTemplate/sections/imageProjectedWithBorderBlock"]},ssr:!0}),contact_us_full_image_block:(0,c.default)(()=>a.e(7450).then(a.bind(a,87450)).catch(()=>l.jsx(l.Fragment,{})),{loadableGenerated:{modules:["components/templates/competencyDetailTemplate/competencyDetailTemplate.js -> @/components/templates/workDetailTemplate/sections/contactUsFullImageBlock"]},ssr:!0})},p=({pageData:e,breadCrumbs:t})=>(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:`pageBnnaer_title ${s().pb_15} banner_title_with_breadcrumb`,children:(0,l.jsxs)("div",{className:"container",children:[l.jsx(n.Z,{crumbs:t}),l.jsx("h1",{children:e?.title})]})}),l.jsx("div",{className:s().comp_list,children:l.jsx(m.bu,{componentList:i,pageData:e})})]})}};