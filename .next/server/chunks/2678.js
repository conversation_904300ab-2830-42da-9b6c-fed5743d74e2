"use strict";exports.id=2678,exports.ids=[2678],exports.modules={12678:(e,r,t)=>{t.r(r),t.d(r,{default:()=>y});var a=t(19510),o=t(68570);let n=(0,o.createProxy)(String.raw`/Users/<USER>/MyFolder/alpago-webapp/components/cards/BenchmarkCard.js`),{__esModule:s,$$typeof:i}=n;n.default,(0,o.createProxy)(String.raw`/Users/<USER>/MyFolder/alpago-webapp/components/cards/BenchmarkCard.js#default`);var c=t(71159);t(32285);var l=t(53699);let p=(0,o.createProxy)(String.raw`/Users/<USER>/MyFolder/alpago-webapp/components/dynamicSection/home/<USER>/Users/<USER>/MyFolder/alpago-webapp/components/dynamicSection/home/<USER>"?")?"&":"?",o=new URLSearchParams({limit:r.toString()}),n=`${e}${a}${o.toString()}`,s=await t(n);return s?.records||[]}),y=async function({content:e}){let r=await m(e?.listing?.request_uri);return a.jsx(g,{content:e,projectList:r})}}};