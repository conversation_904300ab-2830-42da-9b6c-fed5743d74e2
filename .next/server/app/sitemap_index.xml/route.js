(()=>{var e={};e.id=5628,e.ids=[5628],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},97714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>f,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>b,staticGenerationAsyncStorage:()=>h});var i={};r.r(i),r.d(i,{GET:()=>c});var o=r(49303),n=r(88716),s=r(60670),a=r(53699),l=r(98277);function u(e,t){let r=e.filter(e=>e&&""!==e.trim());return`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${r.map(e=>{let r=e.startsWith("/")?`${t}${e}`:`${e}`;return`
  <url>
    <loc>${r}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.5</priority>
  </url>`}).join("")}
</urlset>`}async function c(){try{let{getSiteMap:e}=await (0,a.Z)(),t=await e(),r=(t?.sitemap||[]).map(e=>e.url).filter(e=>e&&""!==e.trim()),i=process.env.NEXTAUTH_URL||process.env.NEXT_PUBLIC_SITE_URL||"",o=[...r],n=u(o,i);return new l.NextResponse(n,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"s-maxage=86400, stale-while-revalidate"}})}catch(t){console.error("Error generating sitemap:",t);let e=u([process.env.NEXTAUTH_URL||process.env.NEXT_PUBLIC_SITE_URL]);return new l.NextResponse(e,{status:200,headers:{"Content-Type":"application/xml"}})}}let d=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/sitemap_index.xml/route",pathname:"/sitemap_index.xml",filename:"route",bundlePath:"app/sitemap_index.xml/route"},resolvedPagePath:"/Users/<USER>/MyFolder/alpago-webapp/app/sitemap_index.xml/route.js",nextConfigOutput:"",userland:i}),{requestAsyncStorage:p,staticGenerationAsyncStorage:h,serverHooks:b}=d,m="/sitemap_index.xml/route";function f(){return(0,s.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:h})}},53699:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});let i={DYNAMIC:{HOME:"api/v2/home/",NAVIGATION:"api/v2/navigation/",COMMON:"api/v2/pages/",CONTACT_FORM:"api/v2/submission/contact-us/",SUBSCRIPTION:"api/v2/submission/subscription/"}};var o=r(29712);let n=process.env.NEXT_PUBLIC_API_BASE_URL;o.Z.create({baseURL:n,headers:{"Content-Type":"application/json"}}),o.Z.create({baseURL:n,headers:{"Content-Type":"application/json"}});let s=o.Z.create({baseURL:n,headers:{"Content-Type":"application/json"}}),a=async()=>(s.interceptors.request.use(async e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>Promise.reject(e)),s);var l=r(6659);let u=async()=>{let e=await a();return{getNavigations:async()=>{try{let t=await e.get(i.DYNAMIC.NAVIGATION);return t?.data}catch(e){return(0,l.Po)(e)}},getPage:async(t,r)=>{try{let o=new URLSearchParams;return r&&o.append("slugTree",r),(await e.get(`${i.DYNAMIC.COMMON}${t}/?${o}`)).data}catch(e){return(0,l.Po)(e)}},getNewsList:async t=>{try{return(await e.get(`${t}`)).data}catch(e){return(0,l.Po)(e)}},getHome:async()=>{try{return(await e.get(`${i.DYNAMIC.HOME}`)).data}catch(e){return(0,l.Po)(e)}},getSiteMap:async t=>{try{return(await e.get("api/v2/sitemap/")).data}catch(e){return(0,l.Po)(e)}}}}},6659:(e,t,r)=>{"use strict";r.d(t,{Po:()=>o,bu:()=>n,qI:()=>s});var i=r(19510);let o=e=>e?.response?.data?{...e.response?.data,status:e.response.status}:{result:"failure",records:[]},n=({componentList:e,pageData:t})=>i.jsx(i.Fragment,{children:t?.content?.map((r,o)=>{let n=e[r.type];if(!n)return null;let s={key:`${r?.type}-${o}`,contentIndex:o,content:r?.value,pageData:t};return i.jsx(n,{...s})})});function s(e){return{title:e?.seo_title||"Alpago",description:e?.seo_description,keywords:e?.seo_keywords,alternates:{canonical:e?.canonical_url},openGraph:{title:e?.og_title,description:e?.og_description,...e?.og_image?{image:e.og_image}:{}}}}},79925:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,n={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[i,o],...n]=a(e),{domain:s,expires:l,httponly:d,maxage:p,path:h,samesite:b,secure:m,partitioned:f,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:i,value:decodeURIComponent(o),domain:s,...l&&{expires:new Date(l)},...d&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:h,...b&&{sameSite:u.includes(t=(t=b).toLowerCase())?t:void 0},...m&&{secure:!0},...g&&{priority:c.includes(r=(r=g).toLowerCase())?r:void 0},...f&&{partitioned:!0}})}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(n,{RequestCookies:()=>d,ResponseCookies:()=>p,parseCookie:()=>a,parseSetCookie:()=>l,stringifyCookie:()=>s}),e.exports=((e,n,s,a)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let s of i(n))o.call(e,s)||void 0===s||t(e,s,{get:()=>n[s],enumerable:!(a=r(n,s))||a.enumerable});return e})(t({},"__esModule",{value:!0}),n);var u=["strict","lax","none"],c=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let o=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(o)?o:function(e){if(!e)return[];var t,r,i,o,n,s=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,n=!1;l();)if(","===(r=e.charAt(a))){for(i=a,a+=1,l(),o=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(n=!0,a=o,s.push(e.substring(t,i)),t=a):a=i+1}else a+=1;(!n||a>=e.length)&&s.push(e.substring(t,e.length))}return s}(o)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[t,r,i]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},18346:(e,t,r)=>{var i;(()=>{var o={226:function(o,n){!function(s,a){"use strict";var l="function",u="undefined",c="object",d="string",p="major",h="model",b="name",m="type",f="vendor",g="version",w="architecture",v="console",x="mobile",y="tablet",P="smarttv",_="wearable",k="embedded",j="Amazon",O="Apple",S="ASUS",R="BlackBerry",A="Browser",C="Chrome",L="Firefox",N="Google",U="Huawei",q="Microsoft",T="Motorola",I="Opera",M="Samsung",E="Sharp",$="Sony",z="Xiaomi",H="Zebra",D="Facebook",B="Chromium OS",G="Mac OS",W=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},F=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},V=function(e,t){return typeof e===d&&-1!==Z(t).indexOf(Z(e))},Z=function(e){return e.toLowerCase()},X=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},Y=function(e,t){for(var r,i,o,n,s,u,d=0;d<t.length&&!s;){var p=t[d],h=t[d+1];for(r=i=0;r<p.length&&!s&&p[r];)if(s=p[r++].exec(e))for(o=0;o<h.length;o++)u=s[++i],typeof(n=h[o])===c&&n.length>0?2===n.length?typeof n[1]==l?this[n[0]]=n[1].call(this,u):this[n[0]]=n[1]:3===n.length?typeof n[1]!==l||n[1].exec&&n[1].test?this[n[0]]=u?u.replace(n[1],n[2]):void 0:this[n[0]]=u?n[1].call(this,u,n[2]):void 0:4===n.length&&(this[n[0]]=u?n[3].call(this,u.replace(n[1],n[2])):void 0):this[n]=u||a;d+=2}},J=function(e,t){for(var r in t)if(typeof t[r]===c&&t[r].length>0){for(var i=0;i<t[r].length;i++)if(V(t[r][i],e))return"?"===r?a:r}else if(V(t[r],e))return"?"===r?a:r;return e},K={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[g,[b,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[g,[b,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[b,g],[/opios[\/ ]+([\w\.]+)/i],[g,[b,I+" Mini"]],[/\bopr\/([\w\.]+)/i],[g,[b,I]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[b,g],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[g,[b,"UC"+A]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[g,[b,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[g,[b,"WeChat"]],[/konqueror\/([\w\.]+)/i],[g,[b,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[g,[b,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[g,[b,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[b,/(.+)/,"$1 Secure "+A],g],[/\bfocus\/([\w\.]+)/i],[g,[b,L+" Focus"]],[/\bopt\/([\w\.]+)/i],[g,[b,I+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[g,[b,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[g,[b,"Dolphin"]],[/coast\/([\w\.]+)/i],[g,[b,I+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[g,[b,"MIUI "+A]],[/fxios\/([-\w\.]+)/i],[g,[b,L]],[/\bqihu|(qi?ho?o?|360)browser/i],[[b,"360 "+A]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[b,/(.+)/,"$1 "+A],g],[/(comodo_dragon)\/([\w\.]+)/i],[[b,/_/g," "],g],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[b,g],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[b],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[b,D],g],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[b,g],[/\bgsa\/([\w\.]+) .*safari\//i],[g,[b,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[g,[b,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[g,[b,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[b,C+" WebView"],g],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[g,[b,"Android "+A]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[b,g],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[g,[b,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[g,b],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[b,[g,J,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[b,g],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[b,"Netscape"],g],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[g,[b,L+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[b,g],[/(cobalt)\/([\w\.]+)/i],[b,[g,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[w,"amd64"]],[/(ia32(?=;))/i],[[w,Z]],[/((?:i[346]|x)86)[;\)]/i],[[w,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[w,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[w,"armhf"]],[/windows (ce|mobile); ppc;/i],[[w,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[w,/ower/,"",Z]],[/(sun4\w)[;\)]/i],[[w,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[w,Z]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[f,M],[m,y]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[f,M],[m,x]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[f,O],[m,x]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[f,O],[m,y]],[/(macintosh);/i],[h,[f,O]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[f,E],[m,x]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[f,U],[m,y]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[f,U],[m,x]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[f,z],[m,x]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[f,z],[m,y]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[f,"OPPO"],[m,x]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[f,"Vivo"],[m,x]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[h,[f,"Realme"],[m,x]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[f,T],[m,x]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[f,T],[m,y]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[f,"LG"],[m,y]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[f,"LG"],[m,x]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[f,"Lenovo"],[m,y]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[f,"Nokia"],[m,x]],[/(pixel c)\b/i],[h,[f,N],[m,y]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[f,N],[m,x]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[f,$],[m,x]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[f,$],[m,y]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[f,"OnePlus"],[m,x]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[f,j],[m,y]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[f,j],[m,x]],[/(playbook);[-\w\),; ]+(rim)/i],[h,f,[m,y]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[f,R],[m,x]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[f,S],[m,y]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[f,S],[m,x]],[/(nexus 9)/i],[h,[f,"HTC"],[m,y]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[f,[h,/_/g," "],[m,x]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[f,"Acer"],[m,y]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[f,"Meizu"],[m,x]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[f,h,[m,x]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[f,h,[m,y]],[/(surface duo)/i],[h,[f,q],[m,y]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[f,"Fairphone"],[m,x]],[/(u304aa)/i],[h,[f,"AT&T"],[m,x]],[/\bsie-(\w*)/i],[h,[f,"Siemens"],[m,x]],[/\b(rct\w+) b/i],[h,[f,"RCA"],[m,y]],[/\b(venue[\d ]{2,7}) b/i],[h,[f,"Dell"],[m,y]],[/\b(q(?:mv|ta)\w+) b/i],[h,[f,"Verizon"],[m,y]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[f,"Barnes & Noble"],[m,y]],[/\b(tm\d{3}\w+) b/i],[h,[f,"NuVision"],[m,y]],[/\b(k88) b/i],[h,[f,"ZTE"],[m,y]],[/\b(nx\d{3}j) b/i],[h,[f,"ZTE"],[m,x]],[/\b(gen\d{3}) b.+49h/i],[h,[f,"Swiss"],[m,x]],[/\b(zur\d{3}) b/i],[h,[f,"Swiss"],[m,y]],[/\b((zeki)?tb.*\b) b/i],[h,[f,"Zeki"],[m,y]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[f,"Dragon Touch"],h,[m,y]],[/\b(ns-?\w{0,9}) b/i],[h,[f,"Insignia"],[m,y]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[f,"NextBook"],[m,y]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[f,"Voice"],h,[m,x]],[/\b(lvtel\-)?(v1[12]) b/i],[[f,"LvTel"],h,[m,x]],[/\b(ph-1) /i],[h,[f,"Essential"],[m,x]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[f,"Envizen"],[m,y]],[/\b(trio[-\w\. ]+) b/i],[h,[f,"MachSpeed"],[m,y]],[/\btu_(1491) b/i],[h,[f,"Rotor"],[m,y]],[/(shield[\w ]+) b/i],[h,[f,"Nvidia"],[m,y]],[/(sprint) (\w+)/i],[f,h,[m,x]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[f,q],[m,x]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[f,H],[m,y]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[f,H],[m,x]],[/smart-tv.+(samsung)/i],[f,[m,P]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[f,M],[m,P]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[f,"LG"],[m,P]],[/(apple) ?tv/i],[f,[h,O+" TV"],[m,P]],[/crkey/i],[[h,C+"cast"],[f,N],[m,P]],[/droid.+aft(\w)( bui|\))/i],[h,[f,j],[m,P]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[f,E],[m,P]],[/(bravia[\w ]+)( bui|\))/i],[h,[f,$],[m,P]],[/(mitv-\w{5}) bui/i],[h,[f,z],[m,P]],[/Hbbtv.*(technisat) (.*);/i],[f,h,[m,P]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[f,X],[h,X],[m,P]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[m,P]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[f,h,[m,v]],[/droid.+; (shield) bui/i],[h,[f,"Nvidia"],[m,v]],[/(playstation [345portablevi]+)/i],[h,[f,$],[m,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[f,q],[m,v]],[/((pebble))app/i],[f,h,[m,_]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[f,O],[m,_]],[/droid.+; (glass) \d/i],[h,[f,N],[m,_]],[/droid.+; (wt63?0{2,3})\)/i],[h,[f,H],[m,_]],[/(quest( 2| pro)?)/i],[h,[f,D],[m,_]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[f,[m,k]],[/(aeobc)\b/i],[h,[f,j],[m,k]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[h,[m,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[m,y]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[m,y]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[m,x]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[f,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[g,[b,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[g,[b,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[b,g],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[g,b]],os:[[/microsoft (windows) (vista|xp)/i],[b,g],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[b,[g,J,K]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[b,"Windows"],[g,J,K]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[g,/_/g,"."],[b,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[b,G],[g,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[g,b],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[b,g],[/\(bb(10);/i],[g,[b,R]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[g,[b,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[g,[b,L+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[g,[b,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[g,[b,"watchOS"]],[/crkey\/([\d\.]+)/i],[g,[b,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[b,B],g],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[b,g],[/(sunos) ?([\w\.\d]*)/i],[[b,"Solaris"],g],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[b,g]]},ee=function(e,t){if(typeof e===c&&(t=e,e=a),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof s!==u&&s.navigator?s.navigator:a,i=e||(r&&r.userAgent?r.userAgent:""),o=r&&r.userAgentData?r.userAgentData:a,n=t?W(Q,t):Q,v=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[b]=a,t[g]=a,Y.call(t,i,n.browser),t[p]=typeof(e=t[g])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:a,v&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[b]="Brave"),t},this.getCPU=function(){var e={};return e[w]=a,Y.call(e,i,n.cpu),e},this.getDevice=function(){var e={};return e[f]=a,e[h]=a,e[m]=a,Y.call(e,i,n.device),v&&!e[m]&&o&&o.mobile&&(e[m]=x),v&&"Macintosh"==e[h]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[h]="iPad",e[m]=y),e},this.getEngine=function(){var e={};return e[b]=a,e[g]=a,Y.call(e,i,n.engine),e},this.getOS=function(){var e={};return e[b]=a,e[g]=a,Y.call(e,i,n.os),v&&!e[b]&&o&&"Unknown"!=o.platform&&(e[b]=o.platform.replace(/chrome os/i,B).replace(/macos/i,G)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===d&&e.length>350?X(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=F([b,g,p]),ee.CPU=F([w]),ee.DEVICE=F([h,f,m,v,x,P,y,_,k]),ee.ENGINE=ee.OS=F([b,g]),typeof n!==u?(o.exports&&(n=o.exports=ee),n.UAParser=ee):r.amdO?void 0!==(i=(function(){return ee}).call(t,r,t,e))&&(e.exports=i):typeof s!==u&&(s.UAParser=ee);var et=typeof s!==u&&(s.jQuery||s.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},n={};function s(e){var t=n[e];if(void 0!==t)return t.exports;var r=n[e]={exports:{}},i=!0;try{o[e].call(r.exports,r,r.exports,s),i=!1}finally{i&&delete n[e]}return r.exports}s.ab=__dirname+"/";var a=s(226);e.exports=a})()},49303:(e,t,r)=>{"use strict";e.exports=r(30517)},44:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PageSignatureError:function(){return r},RemovedPageError:function(){return i},RemovedUAError:function(){return o}});class r extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class i extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class o extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},98277:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ImageResponse:function(){return i.ImageResponse},NextRequest:function(){return o.NextRequest},NextResponse:function(){return n.NextResponse},URLPattern:function(){return a.URLPattern},userAgent:function(){return s.userAgent},userAgentFromString:function(){return s.userAgentFromString}});let i=r(10006),o=r(39745),n=r(59211),s=r(78471),a=r(72433)},79519:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextURL",{enumerable:!0,get:function(){return c}});let i=r(23777),o=r(93817),n=r(61675),s=r(6111),a=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function l(e,t){return new URL(String(e).replace(a,"localhost"),t&&String(t).replace(a,"localhost"))}let u=Symbol("NextURLInternal");class c{constructor(e,t,r){let i,o;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,o=r||{}):o=r||t||{},this[u]={url:l(e,i??o.base),options:o,basePath:""},this.analyze()}analyze(){var e,t,r,o,a;let l=(0,s.getNextPathnameInfo)(this[u].url.pathname,{nextConfig:this[u].options.nextConfig,parseData:!0,i18nProvider:this[u].options.i18nProvider}),c=(0,n.getHostname)(this[u].url,this[u].options.headers);this[u].domainLocale=this[u].options.i18nProvider?this[u].options.i18nProvider.detectDomainLocale(c):(0,i.detectDomainLocale)(null==(t=this[u].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,c);let d=(null==(r=this[u].domainLocale)?void 0:r.defaultLocale)||(null==(a=this[u].options.nextConfig)?void 0:null==(o=a.i18n)?void 0:o.defaultLocale);this[u].url.pathname=l.pathname,this[u].defaultLocale=d,this[u].basePath=l.basePath??"",this[u].buildId=l.buildId,this[u].locale=l.locale??d,this[u].trailingSlash=l.trailingSlash}formatPathname(){return(0,o.formatNextPathnameInfo)({basePath:this[u].basePath,buildId:this[u].buildId,defaultLocale:this[u].options.forceLocale?void 0:this[u].defaultLocale,locale:this[u].locale,pathname:this[u].url.pathname,trailingSlash:this[u].trailingSlash})}formatSearch(){return this[u].url.search}get buildId(){return this[u].buildId}set buildId(e){this[u].buildId=e}get locale(){return this[u].locale??""}set locale(e){var t,r;if(!this[u].locale||!(null==(r=this[u].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[u].locale=e}get defaultLocale(){return this[u].defaultLocale}get domainLocale(){return this[u].domainLocale}get searchParams(){return this[u].url.searchParams}get host(){return this[u].url.host}set host(e){this[u].url.host=e}get hostname(){return this[u].url.hostname}set hostname(e){this[u].url.hostname=e}get port(){return this[u].url.port}set port(e){this[u].url.port=e}get protocol(){return this[u].url.protocol}set protocol(e){this[u].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[u].url=l(e),this.analyze()}get origin(){return this[u].url.origin}get pathname(){return this[u].url.pathname}set pathname(e){this[u].url.pathname=e}get hash(){return this[u].url.hash}set hash(e){this[u].url.hash=e}get search(){return this[u].url.search}set search(e){this[u].url.search=e}get password(){return this[u].url.password}set password(e){this[u].url.password=e}get username(){return this[u].url.username}set username(e){this[u].url.username=e}get basePath(){return this[u].basePath}set basePath(e){this[u].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new c(String(this),this[u].options)}}},92044:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return i.RequestCookies},ResponseCookies:function(){return i.ResponseCookies}});let i=r(79925)},10006:(e,t)=>{"use strict";function r(){throw Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead')}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageResponse",{enumerable:!0,get:function(){return r}})},39745:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERNALS:function(){return a},NextRequest:function(){return l}});let i=r(79519),o=r(17636),n=r(44),s=r(92044),a=Symbol("internal request");class l extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,o.validateURL)(r),e instanceof Request?super(e,t):super(r,t);let n=new i.NextURL(r,{headers:(0,o.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig});this[a]={cookies:new s.RequestCookies(this.headers),geo:t.geo||{},ip:t.ip,nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[a].cookies}get geo(){return this[a].geo}get ip(){return this[a].ip}get nextUrl(){return this[a].nextUrl}get page(){throw new n.RemovedPageError}get ua(){throw new n.RemovedUAError}get url(){return this[a].url}}},59211:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return u}});let i=r(79519),o=r(17636),n=r(92044),s=Symbol("internal response"),a=new Set([301,302,303,307,308]);function l(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[i,o]of e.request.headers)t.set("x-middleware-request-"+i,o),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class u extends Response{constructor(e,t={}){super(e,t),this[s]={cookies:new n.ResponseCookies(this.headers),url:t.url?new i.NextURL(t.url,{headers:(0,o.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[s].cookies}static json(e,t){let r=Response.json(e,t);return new u(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!a.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let i="object"==typeof t?t:{},n=new Headers(null==i?void 0:i.headers);return n.set("Location",(0,o.validateURL)(e)),new u(null,{...i,headers:n,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,o.validateURL)(e)),l(t,r),new u(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),l(e,t),new u(null,{...e,headers:t})}}},72433:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"URLPattern",{enumerable:!0,get:function(){return r}});let r="undefined"==typeof URLPattern?void 0:URLPattern},78471:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isBot:function(){return o},userAgent:function(){return s},userAgentFromString:function(){return n}});let i=function(e){return e&&e.__esModule?e:{default:e}}(r(18346));function o(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function n(e){return{...(0,i.default)(e),isBot:void 0!==e&&o(e)}}function s({headers:e}){return n(e.get("user-agent")||void 0)}},17636:(e,t)=>{"use strict";function r(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function i(e){var t,r,i,o,n,s=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,n=!1;l();)if(","===(r=e.charAt(a))){for(i=a,a+=1,l(),o=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(n=!0,a=o,s.push(e.substring(t,i)),t=a):a=i+1}else a+=1;(!n||a>=e.length)&&s.push(e.substring(t,e.length))}return s}function o(e){let t={},r=[];if(e)for(let[o,n]of e.entries())"set-cookie"===o.toLowerCase()?(r.push(...i(n)),t[o]=1===r.length?r[0]:r):t[o]=n;return t}function n(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromNodeOutgoingHttpHeaders:function(){return r},splitCookiesString:function(){return i},toNodeOutgoingHttpHeaders:function(){return o},validateURL:function(){return n}})},61675:(e,t)=>{"use strict";function r(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getHostname",{enumerable:!0,get:function(){return r}})},23777:(e,t)=>{"use strict";function r(e,t,r){if(e)for(let n of(r&&(r=r.toLowerCase()),e)){var i,o;if(t===(null==(i=n.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===n.defaultLocale.toLowerCase()||(null==(o=n.locales)?void 0:o.some(e=>e.toLowerCase()===r)))return n}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}})},95014:(e,t)=>{"use strict";function r(e,t){let r;let i=e.split("/");return(t||[]).some(t=>!!i[1]&&i[1].toLowerCase()===t.toLowerCase()&&(r=t,i.splice(1,1),e=i.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return r}})},80225:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}});let i=r(91041),o=r(84329);function n(e,t,r,n){if(!t||t===r)return e;let s=e.toLowerCase();return!n&&((0,o.pathHasPrefix)(s,"/api")||(0,o.pathHasPrefix)(s,"/"+t.toLowerCase()))?e:(0,i.addPathPrefix)(e,"/"+t)}},91041:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let i=r(81693);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:o,hash:n}=(0,i.parsePath)(e);return""+t+r+o+n}},98166:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return o}});let i=r(81693);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:o,hash:n}=(0,i.parsePath)(e);return""+r+t+o+n}},93817:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return a}});let i=r(37847),o=r(91041),n=r(98166),s=r(80225);function a(e){let t=(0,s.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,i.removeTrailingSlash)(t)),e.buildId&&(t=(0,n.addPathSuffix)((0,o.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,o.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,n.addPathSuffix)(t,"/"):(0,i.removeTrailingSlash)(t)}},6111:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return s}});let i=r(95014),o=r(29529),n=r(84329);function s(e,t){var r,s;let{basePath:a,i18n:l,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},c={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};a&&(0,n.pathHasPrefix)(c.pathname,a)&&(c.pathname=(0,o.removePathPrefix)(c.pathname,a),c.basePath=a);let d=c.pathname;if(c.pathname.startsWith("/_next/data/")&&c.pathname.endsWith(".json")){let e=c.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];c.buildId=r,d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(c.pathname=d)}if(l){let e=t.i18nProvider?t.i18nProvider.analyze(c.pathname):(0,i.normalizeLocalePath)(c.pathname,l.locales);c.locale=e.detectedLocale,c.pathname=null!=(s=e.pathname)?s:c.pathname,!e.detectedLocale&&c.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,i.normalizeLocalePath)(d,l.locales)).detectedLocale&&(c.locale=e.detectedLocale)}return c}},81693:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},84329:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let i=r(81693);function o(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,i.parsePath)(e);return r===t||r.startsWith(t+"/")}},29529:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return o}});let i=r(84329);function o(e,t){if(!(0,i.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},37847:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,7138],()=>r(97714));module.exports=i})();