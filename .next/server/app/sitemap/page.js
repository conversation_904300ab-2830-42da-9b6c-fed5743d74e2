(()=>{var e={};e.id=5062,e.ids=[5062],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},33026:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,originalPathname:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>p}),r(76082),r(44131),r(35866);var s=r(23191),i=r(88716),a=r(37922),n=r.n(a),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let p=["",{children:["sitemap",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76082)),"/Users/<USER>/MyFolder/alpago-webapp/app/sitemap/page.js"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,44131)),"/Users/<USER>/MyFolder/alpago-webapp/app/layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["/Users/<USER>/MyFolder/alpago-webapp/app/sitemap/page.js"],c="/sitemap/page",d={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/sitemap/page",pathname:"/sitemap",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},35303:()=>{},76082:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>a});var s=r(19510),i=r(53699);r(71159);let a="force-dynamic";async function n(){let{getSiteMap:e}=await (0,i.Z)(),t=function(e){let t=new Map;return e.forEach(e=>{e.title.includes(">")||t.set(e.title.trim(),{title:e.title.trim(),url:e.url,subMenu:[]})}),e.forEach(e=>{if(e.title.includes(">")){let[r,s]=e.title.split(">").map(e=>e.trim());t.has(r)&&t.get(r).subMenu.push({title:s,url:e.url})}}),Array.from(t.values())}(await e().then(e=>e?.sitemap||[]));return s.jsx("div",{className:"sitemap_page",children:(0,s.jsxs)("div",{className:"container",children:[s.jsx("h2",{children:"Sitemap"}),s.jsx("ul",{children:t.map((e,t)=>(0,s.jsxs)("li",{children:[s.jsx("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",children:e.title}),e.subMenu&&s.jsx("ul",{children:e.subMenu.map((e,t)=>s.jsx("li",{children:s.jsx("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",children:e.title})},t))})]},t))})]})})}},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(66621);let i=e=>[{type:"image/x-icon",sizes:"67x67",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,7138,5576,6621,4316],()=>r(33026));module.exports=s})();