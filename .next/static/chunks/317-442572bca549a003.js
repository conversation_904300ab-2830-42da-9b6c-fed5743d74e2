(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[317],{7323:function(t){var e;e=function(){return function(t){function e(o){if(i[o])return i[o].exports;var n=i[o]={exports:{},id:o,loaded:!1};return t[o].call(n.exports,n,n.exports,e),n.loaded=!0,n.exports}var i={};return e.m=t,e.c=i,e.p="dist/",e(0)}([function(t,e,i){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}var n=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o])}return t},s=(o(i(1)),i(6)),r=o(s),a=o(i(7)),l=o(i(8)),c=o(i(9)),h=o(i(10)),u=o(i(11)),d=o(i(14)),p=[],m=!1,f={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,startEvent:"DOMContentLoaded",throttleDelay:99,debounceDelay:50,disableMutationObserver:!1},v=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(t&&(m=!0),m)return p=(0,u.default)(p,f),(0,h.default)(p,f.once),p},g=function(){p=(0,d.default)(),v()},b=function(){p.forEach(function(t,e){t.node.removeAttribute("data-aos"),t.node.removeAttribute("data-aos-easing"),t.node.removeAttribute("data-aos-duration"),t.node.removeAttribute("data-aos-delay")})};t.exports={init:function(t){f=n(f,t),p=(0,d.default)();var e,i=document.all&&!window.atob;return!0===(e=f.disable)||"mobile"===e&&c.default.mobile()||"phone"===e&&c.default.phone()||"tablet"===e&&c.default.tablet()||"function"==typeof e&&!0===e()||i?b():(f.disableMutationObserver||l.default.isSupported()||(console.info('\n      aos: MutationObserver is not supported on this browser,\n      code mutations observing has been disabled.\n      You may have to call "refreshHard()" by yourself.\n    '),f.disableMutationObserver=!0),document.querySelector("body").setAttribute("data-aos-easing",f.easing),document.querySelector("body").setAttribute("data-aos-duration",f.duration),document.querySelector("body").setAttribute("data-aos-delay",f.delay),"DOMContentLoaded"===f.startEvent&&["complete","interactive"].indexOf(document.readyState)>-1?v(!0):"load"===f.startEvent?window.addEventListener(f.startEvent,function(){v(!0)}):document.addEventListener(f.startEvent,function(){v(!0)}),window.addEventListener("resize",(0,a.default)(v,f.debounceDelay,!0)),window.addEventListener("orientationchange",(0,a.default)(v,f.debounceDelay,!0)),window.addEventListener("scroll",(0,r.default)(function(){(0,h.default)(p,f.once)},f.throttleDelay)),f.disableMutationObserver||l.default.ready("[data-aos]",g),p)},refresh:v,refreshHard:g}},function(t,e){},,,,,function(t,e){(function(e){"use strict";function i(t){var e=void 0===t?"undefined":n(t);return!!t&&("object"==e||"function"==e)}function o(t){if("number"==typeof t)return t;if("symbol"==(void 0===(e=t)?"undefined":n(e))||e&&"object"==(void 0===e?"undefined":n(e))&&v.call(e)==a)return r;if(i(t)){var e,o="function"==typeof t.valueOf?t.valueOf():t;t=i(o)?o+"":o}if("string"!=typeof t)return 0===t?t:+t;var s=h.test(t=t.replace(l,""));return s||u.test(t)?d(t.slice(2),s?2:8):c.test(t)?r:+t}var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s="Expected a function",r=NaN,a="[object Symbol]",l=/^\s+|\s+$/g,c=/^[-+]0x[0-9a-f]+$/i,h=/^0b[01]+$/i,u=/^0o[0-7]+$/i,d=parseInt,p="object"==(void 0===e?"undefined":n(e))&&e&&e.Object===Object&&e,m="object"==("undefined"==typeof self?"undefined":n(self))&&self&&self.Object===Object&&self,f=p||m||Function("return this")(),v=Object.prototype.toString,g=Math.max,b=Math.min,w=function(){return f.Date.now()};t.exports=function(t,e,n){var r=!0,a=!0;if("function"!=typeof t)throw TypeError(s);return i(n)&&(r="leading"in n?!!n.leading:r,a="trailing"in n?!!n.trailing:a),function(t,e,n){function r(e){var i=u,o=d;return u=d=void 0,y=e,m=t.apply(o,i)}function a(t){var i=t-v,o=t-y;return void 0===v||i>=e||i<0||k&&o>=p}function l(){var t,i,o,n=w();return a(n)?c(n):void(f=setTimeout(l,(t=n-v,i=n-y,o=e-t,k?b(o,p-i):o)))}function c(t){return f=void 0,_&&u?r(t):(u=d=void 0,m)}function h(){var t,i=w(),o=a(i);if(u=arguments,d=this,v=i,o){if(void 0===f)return y=t=v,f=setTimeout(l,e),S?r(t):m;if(k)return f=setTimeout(l,e),r(v)}return void 0===f&&(f=setTimeout(l,e)),m}var u,d,p,m,f,v,y=0,S=!1,k=!1,_=!0;if("function"!=typeof t)throw TypeError(s);return e=o(e)||0,i(n)&&(S=!!n.leading,p=(k="maxWait"in n)?g(o(n.maxWait)||0,e):p,_="trailing"in n?!!n.trailing:_),h.cancel=function(){void 0!==f&&clearTimeout(f),y=0,u=v=d=f=void 0},h.flush=function(){return void 0===f?m:c(w())},h}(t,e,{leading:r,maxWait:e,trailing:a})}}).call(e,function(){return this}())},function(t,e){(function(e){"use strict";function i(t){var e=void 0===t?"undefined":n(t);return!!t&&("object"==e||"function"==e)}function o(t){if("number"==typeof t)return t;if("symbol"==(void 0===(e=t)?"undefined":n(e))||e&&"object"==(void 0===e?"undefined":n(e))&&f.call(e)==r)return s;if(i(t)){var e,o="function"==typeof t.valueOf?t.valueOf():t;t=i(o)?o+"":o}if("string"!=typeof t)return 0===t?t:+t;var d=c.test(t=t.replace(a,""));return d||h.test(t)?u(t.slice(2),d?2:8):l.test(t)?s:+t}var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s=NaN,r="[object Symbol]",a=/^\s+|\s+$/g,l=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,h=/^0o[0-7]+$/i,u=parseInt,d="object"==(void 0===e?"undefined":n(e))&&e&&e.Object===Object&&e,p="object"==("undefined"==typeof self?"undefined":n(self))&&self&&self.Object===Object&&self,m=d||p||Function("return this")(),f=Object.prototype.toString,v=Math.max,g=Math.min,b=function(){return m.Date.now()};t.exports=function(t,e,n){function s(e){var i=h,o=u;return h=u=void 0,w=e,p=t.apply(o,i)}function r(t){var i=t-f,o=t-w;return void 0===f||i>=e||i<0||S&&o>=d}function a(){var t,i,o,n=b();return r(n)?l(n):void(m=setTimeout(a,(t=n-f,i=n-w,o=e-t,S?g(o,d-i):o)))}function l(t){return m=void 0,k&&h?s(t):(h=u=void 0,p)}function c(){var t,i=b(),o=r(i);if(h=arguments,u=this,f=i,o){if(void 0===m)return w=t=f,m=setTimeout(a,e),y?s(t):p;if(S)return m=setTimeout(a,e),s(f)}return void 0===m&&(m=setTimeout(a,e)),p}var h,u,d,p,m,f,w=0,y=!1,S=!1,k=!0;if("function"!=typeof t)throw TypeError("Expected a function");return e=o(e)||0,i(n)&&(y=!!n.leading,d=(S="maxWait"in n)?v(o(n.maxWait)||0,e):d,k="trailing"in n?!!n.trailing:k),c.cancel=function(){void 0!==m&&clearTimeout(m),w=0,h=f=u=m=void 0},c.flush=function(){return void 0===m?p:l(b())},c}}).call(e,function(){return this}())},function(t,e){"use strict";function i(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function o(t){t&&t.forEach(function(t){var e=Array.prototype.slice.call(t.addedNodes),i=Array.prototype.slice.call(t.removedNodes);if(function t(e){var i=void 0,o=void 0;for(i=0;i<e.length;i+=1)if((o=e[i]).dataset&&o.dataset.aos||o.children&&t(o.children))return!0;return!1}(e.concat(i)))return n()})}Object.defineProperty(e,"__esModule",{value:!0});var n=function(){};e.default={isSupported:function(){return!!i()},ready:function(t,e){var s=window.document,r=new(i())(o);n=e,r.observe(s.documentElement,{childList:!0,subtree:!0,removedNodes:!0})}}},function(t,e){"use strict";function i(){return navigator.userAgent||navigator.vendor||window.opera||""}Object.defineProperty(e,"__esModule",{value:!0});var o=function(){function t(t,e){for(var i=0;i<e.length;i++){var o=e[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(e,i,o){return i&&t(e.prototype,i),o&&t(e,o),e}}(),n=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,s=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,r=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i,a=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,l=function(){function t(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,t)}return o(t,[{key:"phone",value:function(){var t=i();return!(!n.test(t)&&!s.test(t.substr(0,4)))}},{key:"mobile",value:function(){var t=i();return!(!r.test(t)&&!a.test(t.substr(0,4)))}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}}]),t}();e.default=new l},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=function(t,e,i){var o=t.node.getAttribute("data-aos-once");e>t.position?t.node.classList.add("aos-animate"):void 0===o||"false"!==o&&(i||"true"===o)||t.node.classList.remove("aos-animate")};e.default=function(t,e){var o=window.pageYOffset,n=window.innerHeight;t.forEach(function(t,s){i(t,n+o,e)})}},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o,n=(o=i(12))&&o.__esModule?o:{default:o};e.default=function(t,e){return t.forEach(function(t,i){t.node.classList.add("aos-init"),t.position=(0,n.default)(t.node,e.offset)}),t}},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o,n=(o=i(13))&&o.__esModule?o:{default:o};e.default=function(t,e){var i=0,o=0,s=window.innerHeight,r={offset:t.getAttribute("data-aos-offset"),anchor:t.getAttribute("data-aos-anchor"),anchorPlacement:t.getAttribute("data-aos-anchor-placement")};switch(r.offset&&!isNaN(r.offset)&&(o=parseInt(r.offset)),r.anchor&&document.querySelectorAll(r.anchor)&&(t=document.querySelectorAll(r.anchor)[0]),i=(0,n.default)(t).top,r.anchorPlacement){case"top-bottom":break;case"center-bottom":i+=t.offsetHeight/2;break;case"bottom-bottom":i+=t.offsetHeight;break;case"top-center":i+=s/2;break;case"bottom-center":i+=s/2+t.offsetHeight;break;case"center-center":i+=s/2+t.offsetHeight/2;break;case"top-top":i+=s;break;case"bottom-top":i+=t.offsetHeight+s;break;case"center-top":i+=t.offsetHeight/2+s}return r.anchorPlacement||r.offset||isNaN(e)||(o=e),i+o}},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){for(var e=0,i=0;t&&!isNaN(t.offsetLeft)&&!isNaN(t.offsetTop);)e+=t.offsetLeft-("BODY"!=t.tagName?t.scrollLeft:0),i+=t.offsetTop-("BODY"!=t.tagName?t.scrollTop:0),t=t.offsetParent;return{top:i,left:e}}},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return t=t||document.querySelectorAll("[data-aos]"),Array.prototype.map.call(t,function(t){return{node:t}})}}])},t.exports=e()},3023:function(){},8364:function(t,e,i){"use strict";function o(t,e,i){return Math.max(t,Math.min(e,i))}i.d(e,{Z:function(){return c}});class n{advance(t){var e,i,n;if(!this.isRunning)return;let s=!1;if(this.lerp)this.value=(e=this.value,i=this.to,(1-(n=1-Math.exp(-(60*this.lerp)*t)))*e+n*i),Math.round(this.value)===this.to&&(this.value=this.to,s=!0);else{this.currentTime+=t;let e=o(0,this.currentTime/this.duration,1),i=(s=e>=1)?1:this.easing(e);this.value=this.from+(this.to-this.from)*i}this.onUpdate?.(this.value,s),s&&this.stop()}stop(){this.isRunning=!1}fromTo(t,e,{lerp:i=.1,duration:o=1,easing:n=t=>t,onStart:s,onUpdate:r}){this.from=this.value=t,this.to=e,this.lerp=i,this.duration=o,this.easing=n,this.currentTime=0,this.isRunning=!0,s?.(),this.onUpdate=r}}class s{constructor({wrapper:t,content:e,autoResize:i=!0,debounce:o=250}={}){var n;let s;this.wrapper=t,this.content=e,i&&(this.debouncedResize=(n=this.resize,function(){let t=arguments,e=this;clearTimeout(s),s=setTimeout(function(){n.apply(e,t)},o)}),this.wrapper===window?window.addEventListener("resize",this.debouncedResize,!1):(this.wrapperResizeObserver=new ResizeObserver(this.debouncedResize),this.wrapperResizeObserver.observe(this.wrapper)),this.contentResizeObserver=new ResizeObserver(this.debouncedResize),this.contentResizeObserver.observe(this.content)),this.resize()}destroy(){this.wrapperResizeObserver?.disconnect(),this.contentResizeObserver?.disconnect(),window.removeEventListener("resize",this.debouncedResize,!1)}resize=()=>{this.onWrapperResize(),this.onContentResize()};onWrapperResize=()=>{this.wrapper===window?(this.width=window.innerWidth,this.height=window.innerHeight):(this.width=this.wrapper.clientWidth,this.height=this.wrapper.clientHeight)};onContentResize=()=>{this.wrapper===window?(this.scrollHeight=this.content.scrollHeight,this.scrollWidth=this.content.scrollWidth):(this.scrollHeight=this.wrapper.scrollHeight,this.scrollWidth=this.wrapper.scrollWidth)};get limit(){return{x:this.scrollWidth-this.width,y:this.scrollHeight-this.height}}}class r{constructor(){this.events={}}emit(t,...e){let i=this.events[t]||[];for(let t=0,o=i.length;t<o;t++)i[t](...e)}on(t,e){return this.events[t]?.push(e)||(this.events[t]=[e]),()=>{this.events[t]=this.events[t]?.filter(t=>e!==t)}}off(t,e){this.events[t]=this.events[t]?.filter(t=>e!==t)}destroy(){this.events={}}}let a=100/6;class l{constructor(t,{wheelMultiplier:e=1,touchMultiplier:i=1}){this.element=t,this.wheelMultiplier=e,this.touchMultiplier=i,this.touchStart={x:null,y:null},this.emitter=new r,window.addEventListener("resize",this.onWindowResize,!1),this.onWindowResize(),this.element.addEventListener("wheel",this.onWheel,{passive:!1}),this.element.addEventListener("touchstart",this.onTouchStart,{passive:!1}),this.element.addEventListener("touchmove",this.onTouchMove,{passive:!1}),this.element.addEventListener("touchend",this.onTouchEnd,{passive:!1})}on(t,e){return this.emitter.on(t,e)}destroy(){this.emitter.destroy(),window.removeEventListener("resize",this.onWindowResize,!1),this.element.removeEventListener("wheel",this.onWheel,{passive:!1}),this.element.removeEventListener("touchstart",this.onTouchStart,{passive:!1}),this.element.removeEventListener("touchmove",this.onTouchMove,{passive:!1}),this.element.removeEventListener("touchend",this.onTouchEnd,{passive:!1})}onTouchStart=t=>{let{clientX:e,clientY:i}=t.targetTouches?t.targetTouches[0]:t;this.touchStart.x=e,this.touchStart.y=i,this.lastDelta={x:0,y:0},this.emitter.emit("scroll",{deltaX:0,deltaY:0,event:t})};onTouchMove=t=>{let{clientX:e,clientY:i}=t.targetTouches?t.targetTouches[0]:t,o=-(e-this.touchStart.x)*this.touchMultiplier,n=-(i-this.touchStart.y)*this.touchMultiplier;this.touchStart.x=e,this.touchStart.y=i,this.lastDelta={x:o,y:n},this.emitter.emit("scroll",{deltaX:o,deltaY:n,event:t})};onTouchEnd=t=>{this.emitter.emit("scroll",{deltaX:this.lastDelta.x,deltaY:this.lastDelta.y,event:t})};onWheel=t=>{let{deltaX:e,deltaY:i,deltaMode:o}=t;e*=1===o?a:2===o?this.windowWidth:1,i*=1===o?a:2===o?this.windowHeight:1,e*=this.wheelMultiplier,i*=this.wheelMultiplier,this.emitter.emit("scroll",{deltaX:e,deltaY:i,event:t})};onWindowResize=()=>{this.windowWidth=window.innerWidth,this.windowHeight=window.innerHeight}}class c{constructor({wrapper:t=window,content:e=document.documentElement,wheelEventsTarget:i=t,eventsTarget:o=i,smoothWheel:a=!0,syncTouch:c=!1,syncTouchLerp:h=.075,touchInertiaMultiplier:u=35,duration:d,easing:p=t=>Math.min(1,1.001-Math.pow(2,-10*t)),lerp:m=!d&&.1,infinite:f=!1,orientation:v="vertical",gestureOrientation:g="vertical",touchMultiplier:b=1,wheelMultiplier:w=1,autoResize:y=!0,__experimental__naiveDimensions:S=!1}={}){this.__isSmooth=!1,this.__isScrolling=!1,this.__isStopped=!1,this.__isLocked=!1,this.onVirtualScroll=({deltaX:t,deltaY:e,event:i})=>{if(i.ctrlKey)return;let o=i.type.includes("touch"),n=i.type.includes("wheel");if(this.options.syncTouch&&o&&"touchstart"===i.type&&!this.isStopped&&!this.isLocked)return void this.reset();let s="vertical"===this.options.gestureOrientation&&0===e||"horizontal"===this.options.gestureOrientation&&0===t;if(0===t&&0===e||s)return;let r=i.composedPath();if((r=r.slice(0,r.indexOf(this.rootElement))).find(t=>{var e,i,s,r,a;return(null===(e=t.hasAttribute)||void 0===e?void 0:e.call(t,"data-lenis-prevent"))||o&&(null===(i=t.hasAttribute)||void 0===i?void 0:i.call(t,"data-lenis-prevent-touch"))||n&&(null===(s=t.hasAttribute)||void 0===s?void 0:s.call(t,"data-lenis-prevent-wheel"))||(null===(r=t.classList)||void 0===r?void 0:r.contains("lenis"))&&!(null===(a=t.classList)||void 0===a?void 0:a.contains("lenis-stopped"))}))return;if(this.isStopped||this.isLocked)return void i.preventDefault();if(this.isSmooth=this.options.syncTouch&&o||this.options.smoothWheel&&n,!this.isSmooth)return this.isScrolling=!1,void this.animate.stop();i.preventDefault();let a=e;"both"===this.options.gestureOrientation?a=Math.abs(e)>Math.abs(t)?e:t:"horizontal"===this.options.gestureOrientation&&(a=t);let l=o&&this.options.syncTouch,c=o&&"touchend"===i.type&&Math.abs(a)>5;c&&(a=this.velocity*this.options.touchInertiaMultiplier),this.scrollTo(this.targetScroll+a,Object.assign({programmatic:!1},l?{lerp:c?this.options.syncTouchLerp:1}:{lerp:this.options.lerp,duration:this.options.duration,easing:this.options.easing}))},this.onNativeScroll=()=>{if(!this.__preventNextScrollEvent&&!this.isScrolling){let t=this.animatedScroll;this.animatedScroll=this.targetScroll=this.actualScroll,this.velocity=0,this.direction=Math.sign(this.animatedScroll-t),this.emit()}},window.lenisVersion="1.0.42",t!==document.documentElement&&t!==document.body||(t=window),this.options={wrapper:t,content:e,wheelEventsTarget:i,eventsTarget:o,smoothWheel:a,syncTouch:c,syncTouchLerp:h,touchInertiaMultiplier:u,duration:d,easing:p,lerp:m,infinite:f,gestureOrientation:g,orientation:v,touchMultiplier:b,wheelMultiplier:w,autoResize:y,__experimental__naiveDimensions:S},this.animate=new n,this.emitter=new r,this.dimensions=new s({wrapper:t,content:e,autoResize:y}),this.toggleClassName("lenis",!0),this.velocity=0,this.isLocked=!1,this.isStopped=!1,this.isSmooth=c||a,this.isScrolling=!1,this.targetScroll=this.animatedScroll=this.actualScroll,this.options.wrapper.addEventListener("scroll",this.onNativeScroll,!1),this.virtualScroll=new l(o,{touchMultiplier:b,wheelMultiplier:w}),this.virtualScroll.on("scroll",this.onVirtualScroll)}destroy(){this.emitter.destroy(),this.options.wrapper.removeEventListener("scroll",this.onNativeScroll,!1),this.virtualScroll.destroy(),this.dimensions.destroy(),this.toggleClassName("lenis",!1),this.toggleClassName("lenis-smooth",!1),this.toggleClassName("lenis-scrolling",!1),this.toggleClassName("lenis-stopped",!1),this.toggleClassName("lenis-locked",!1)}on(t,e){return this.emitter.on(t,e)}off(t,e){return this.emitter.off(t,e)}setScroll(t){this.isHorizontal?this.rootElement.scrollLeft=t:this.rootElement.scrollTop=t}resize(){this.dimensions.resize()}emit(){this.emitter.emit("scroll",this)}reset(){this.isLocked=!1,this.isScrolling=!1,this.animatedScroll=this.targetScroll=this.actualScroll,this.velocity=0,this.animate.stop()}start(){this.isStopped&&(this.isStopped=!1,this.reset())}stop(){this.isStopped||(this.isStopped=!0,this.animate.stop(),this.reset())}raf(t){let e=t-(this.time||t);this.time=t,this.animate.advance(.001*e)}scrollTo(t,{offset:e=0,immediate:i=!1,lock:n=!1,duration:s=this.options.duration,easing:r=this.options.easing,lerp:a=!s&&this.options.lerp,onComplete:l,force:c=!1,programmatic:h=!0}={}){if(!this.isStopped&&!this.isLocked||c){if(["top","left","start"].includes(t))t=0;else if(["bottom","right","end"].includes(t))t=this.limit;else{let i;if("string"==typeof t?i=document.querySelector(t):(null==t?void 0:t.nodeType)&&(i=t),i){if(this.options.wrapper!==window){let t=this.options.wrapper.getBoundingClientRect();e-=this.isHorizontal?t.left:t.top}let o=i.getBoundingClientRect();t=(this.isHorizontal?o.left:o.top)+this.animatedScroll}}if("number"==typeof t){if(t+=e,t=Math.round(t),this.options.infinite?h&&(this.targetScroll=this.animatedScroll=this.scroll):t=o(0,t,this.limit),i)return this.animatedScroll=this.targetScroll=t,this.setScroll(this.scroll),this.reset(),void(null==l||l(this));if(!h){if(t===this.targetScroll)return;this.targetScroll=t}this.animate.fromTo(this.animatedScroll,t,{duration:s,easing:r,lerp:a,onStart:()=>{n&&(this.isLocked=!0),this.isScrolling=!0},onUpdate:(t,e)=>{this.isScrolling=!0,this.velocity=t-this.animatedScroll,this.direction=Math.sign(this.velocity),this.animatedScroll=t,this.setScroll(this.scroll),h&&(this.targetScroll=t),e||this.emit(),e&&(this.reset(),this.emit(),null==l||l(this),this.__preventNextScrollEvent=!0,requestAnimationFrame(()=>{delete this.__preventNextScrollEvent}))}})}}}get rootElement(){return this.options.wrapper===window?document.documentElement:this.options.wrapper}get limit(){return this.options.__experimental__naiveDimensions?this.isHorizontal?this.rootElement.scrollWidth-this.rootElement.clientWidth:this.rootElement.scrollHeight-this.rootElement.clientHeight:this.dimensions.limit[this.isHorizontal?"x":"y"]}get isHorizontal(){return"horizontal"===this.options.orientation}get actualScroll(){return this.isHorizontal?this.rootElement.scrollLeft:this.rootElement.scrollTop}get scroll(){var t;return this.options.infinite?(this.animatedScroll%(t=this.limit)+t)%t:this.animatedScroll}get progress(){return 0===this.limit?1:this.scroll/this.limit}get isSmooth(){return this.__isSmooth}set isSmooth(t){this.__isSmooth!==t&&(this.__isSmooth=t,this.toggleClassName("lenis-smooth",t))}get isScrolling(){return this.__isScrolling}set isScrolling(t){this.__isScrolling!==t&&(this.__isScrolling=t,this.toggleClassName("lenis-scrolling",t))}get isStopped(){return this.__isStopped}set isStopped(t){this.__isStopped!==t&&(this.__isStopped=t,this.toggleClassName("lenis-stopped",t))}get isLocked(){return this.__isLocked}set isLocked(t){this.__isLocked!==t&&(this.__isLocked=t,this.toggleClassName("lenis-locked",t))}get className(){let t="lenis";return this.isStopped&&(t+=" lenis-stopped"),this.isLocked&&(t+=" lenis-locked"),this.isScrolling&&(t+=" lenis-scrolling"),this.isSmooth&&(t+=" lenis-smooth"),t}toggleClassName(t,e){this.rootElement.classList.toggle(t,e),this.emitter.emit("className change",this)}}}}]);