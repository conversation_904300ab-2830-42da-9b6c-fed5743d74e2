"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[736],{19:function(e,t,n){n.d(t,{V:function(){return l}});var r=n(2265),i=n(9582);/*!
 * @gsap/react 2.1.2
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license or for
 * Club GSAP members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
*/let o="undefined"!=typeof document?r.useLayoutEffect:r.useEffect,s=e=>e&&!Array.isArray(e)&&"object"==typeof e,c=[],a={},u=i.ZP,l=(e,t=c)=>{let n=a;s(e)?(n=e,e=null,t="dependencies"in n?n.dependencies:c):s(t)&&(t="dependencies"in(n=t)?n.dependencies:c),e&&"function"!=typeof e&&console.warn("First parameter must be a function or config object");let{scope:i,revertOnUpdate:l}=n,d=(0,r.useRef)(!1),f=(0,r.useRef)(u.context(()=>{},i)),h=(0,r.useRef)(e=>f.current.add(null,e)),m=t&&t.length&&!l;return m&&o(()=>(d.current=!0,()=>f.current.revert()),c),o(()=>{if(e&&f.current.add(e,i),!m||!d.current)return()=>f.current.revert()},t),{context:f.current,contextSafe:h.current}};l.register=e=>{u=e},l.headless=!0},4203:function(e,t){t.E=function(e,t){return e.split(",").map(function(e){var t=(e=e.trim()).match(n),o=t[1],s=t[2],c=t[3]||"",a={};return a.inverse=!!o&&"not"===o.toLowerCase(),a.type=s?s.toLowerCase():"all",c=c.match(/\([^\)]+\)/g)||[],a.expressions=c.map(function(e){var t=e.match(r),n=t[1].toLowerCase().match(i);return{modifier:n[1],feature:n[2],value:t[2]}}),a}).some(function(e){var n=e.inverse,r="all"===e.type||t.type===e.type;if(r&&n||!(r||n))return!1;var i=e.expressions.every(function(e){var n=e.feature,r=e.modifier,i=e.value,o=t[n];if(!o)return!1;switch(n){case"orientation":case"scan":return o.toLowerCase()===i.toLowerCase();case"width":case"height":case"device-width":case"device-height":i=u(i),o=u(o);break;case"resolution":i=a(i),o=a(o);break;case"aspect-ratio":case"device-aspect-ratio":case"device-pixel-ratio":i=c(i),o=c(o);break;case"grid":case"color":case"color-index":case"monochrome":i=parseInt(i,10)||1,o=parseInt(o,10)||0}switch(r){case"min":return o>=i;case"max":return o<=i;default:return o===i}});return i&&!n||!i&&n})};var n=/(?:(only|not)?\s*([^\s\(\)]+)(?:\s*and)?\s*)?(.+)?/i,r=/\(\s*([^\s\:\)]+)\s*(?:\:\s*([^\s\)]+))?\s*\)/,i=/^(?:(min|max)-)?(.+)/,o=/(em|rem|px|cm|mm|in|pt|pc)?$/,s=/(dpi|dpcm|dppx)?$/;function c(e){var t,n=Number(e);return n||(n=(t=e.match(/^(\d+)\s*\/\s*(\d+)$/))[1]/t[2]),n}function a(e){var t=parseFloat(e);switch(String(e).match(s)[1]){case"dpcm":return t/2.54;case"dppx":return 96*t;default:return t}}function u(e){var t=parseFloat(e);switch(String(e).match(o)[1]){case"em":case"rem":return 16*t;case"cm":return 96*t/2.54;case"mm":return 96*t/2.54/10;case"in":return 96*t;case"pt":return 72*t;case"pc":return 72*t/12;default:return t}}},8155:function(e,t,n){var r=n(4203).E,i="undefined"!=typeof window?window.matchMedia:null;function o(e,t,n){var o,s=this;function c(e){s.matches=e.matches,s.media=e.media}i&&!n&&(o=i.call(window,e)),o?(this.matches=o.matches,this.media=o.media,o.addListener(c)):(this.matches=r(e,t),this.media=e),this.addListener=function(e){o&&o.addListener(e)},this.removeListener=function(e){o&&o.removeListener(e)},this.dispose=function(){o&&o.removeListener(c)}}e.exports=function(e,t,n){return new o(e,t,n)}},2916:function(e,t,n){n.d(t,{ac:function(){return M}});var r=n(2265),i=n(8155),o=n.n(i),s=/[A-Z]/g,c=/^ms-/,a={};function u(e){return"-"+e.toLowerCase()}var l=function(e){if(a.hasOwnProperty(e))return a[e];var t=e.replace(s,u);return a[e]=c.test(t)?"-"+t:t},d=n(1448),f=n.n(d);let h=f().oneOfType([f().string,f().number]),m={all:f().bool,grid:f().bool,aural:f().bool,braille:f().bool,handheld:f().bool,print:f().bool,projection:f().bool,screen:f().bool,tty:f().bool,tv:f().bool,embossed:f().bool},{type:p,...v}={orientation:f().oneOf(["portrait","landscape"]),scan:f().oneOf(["progressive","interlace"]),aspectRatio:f().string,deviceAspectRatio:f().string,height:h,deviceHeight:h,width:h,deviceWidth:h,color:f().bool,colorIndex:f().bool,monochrome:f().bool,resolution:h,type:Object.keys(m)},g={minAspectRatio:f().string,maxAspectRatio:f().string,minDeviceAspectRatio:f().string,maxDeviceAspectRatio:f().string,minHeight:h,maxHeight:h,minDeviceHeight:h,maxDeviceHeight:h,minWidth:h,maxWidth:h,minDeviceWidth:h,maxDeviceWidth:h,minColor:f().number,maxColor:f().number,minColorIndex:f().number,maxColorIndex:f().number,minMonochrome:f().number,maxMonochrome:f().number,minResolution:h,maxResolution:h,...v};var b={...m,...g};let y=e=>`not ${e}`,x=(e,t)=>{let n=l(e);return("number"==typeof t&&(t=`${t}px`),!0===t)?n:!1===t?y(n):`(${n}: ${t})`},w=e=>e.join(" and "),E=e=>{let t=[];return Object.keys(b).forEach(n=>{let r=e[n];null!=r&&t.push(x(n,r))}),w(t)},R=(0,r.createContext)(void 0),C=e=>e.query||E(e),L=e=>{if(e)return Object.keys(e).reduce((t,n)=>(t[l(n)]=e[n],t),{})},j=()=>{let e=(0,r.useRef)(!1);return(0,r.useEffect)(()=>{e.current=!0},[]),e.current},O=e=>{let t=(0,r.useContext)(R),n=()=>L(e)||L(t),[i,o]=(0,r.useState)(n);return(0,r.useEffect)(()=>{let e=n();!function(e,t){if(e===t)return!0;if(!e||!t)return!1;let n=Object.keys(e),r=Object.keys(t),i=n.length;if(r.length!==i)return!1;for(let r=0;r<i;r++){let i=n[r];if(e[i]!==t[i]||!Object.prototype.hasOwnProperty.call(t,i))return!1}return!0}(i,e)&&o(e)},[e,t]),i},k=e=>{let t=()=>C(e),[n,i]=(0,r.useState)(t);return(0,r.useEffect)(()=>{let e=t();n!==e&&i(e)},[e]),n},I=(e,t)=>{let n=()=>o()(e,t||{},!!t),[i,s]=(0,r.useState)(n),c=j();return(0,r.useEffect)(()=>{if(c){let e=n();return s(e),()=>{e&&e.dispose()}}},[e,t]),i},S=e=>{let[t,n]=(0,r.useState)(e.matches);return(0,r.useEffect)(()=>{let t=e=>{n(e.matches)};return e.addListener(t),n(e.matches),()=>{e.removeListener(t)}},[e]),t},M=(e,t,n)=>{let i=O(t),o=k(e);if(!o)throw Error("Invalid or missing MediaQuery!");let s=I(o,i),c=S(s),a=j();return(0,r.useEffect)(()=>{a&&n&&n(c)},[c]),(0,r.useEffect)(()=>()=>{s&&s.dispose()},[]),c}},4446:function(e,t,n){n.d(t,{M:function(){return g}});var r=n(7437),i=n(2265),o=n(5050),s=n(458),c=n(7797),a=n(9791);class u extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function l(e){let{children:t,isPresent:n}=e,o=(0,i.useId)(),s=(0,i.useRef)(null),c=(0,i.useRef)({width:0,height:0,top:0,left:0}),{nonce:l}=(0,i.useContext)(a._);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i}=c.current;if(n||!s.current||!e||!t)return;s.current.dataset.motionPopId=o;let a=document.createElement("style");return l&&(a.nonce=l),document.head.appendChild(a),a.sheet&&a.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(r,"px !important;\n            left: ").concat(i,"px !important;\n          }\n        ")),()=>{document.head.removeChild(a)}},[n]),(0,r.jsx)(u,{isPresent:n,childRef:s,sizeRef:c,children:i.cloneElement(t,{ref:s})})}let d=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:a,custom:u,presenceAffectsLayout:d,mode:h}=e,m=(0,s.h)(f),p=(0,i.useId)(),v=(0,i.useCallback)(e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;a&&a()},[m,a]),g=(0,i.useMemo)(()=>({id:p,initial:n,isPresent:o,custom:u,onExitComplete:v,register:e=>(m.set(e,!1),()=>m.delete(e))}),d?[Math.random(),v]:[o,v]);return(0,i.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[o]),i.useEffect(()=>{o||m.size||!a||a()},[o]),"popLayout"===h&&(t=(0,r.jsx)(l,{isPresent:o,children:t})),(0,r.jsx)(c.O.Provider,{value:g,children:t})};function f(){return new Map}var h=n(3241);let m=e=>e.key||"";function p(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}var v=n(9033);let g=e=>{let{children:t,custom:n,initial:c=!0,onExitComplete:a,presenceAffectsLayout:u=!0,mode:l="sync",propagate:f=!1}=e,[g,b]=(0,h.oO)(f),y=(0,i.useMemo)(()=>p(t),[t]),x=f&&!g?[]:y.map(m),w=(0,i.useRef)(!0),E=(0,i.useRef)(y),R=(0,s.h)(()=>new Map),[C,L]=(0,i.useState)(y),[j,O]=(0,i.useState)(y);(0,v.L)(()=>{w.current=!1,E.current=y;for(let e=0;e<j.length;e++){let t=m(j[e]);x.includes(t)?R.delete(t):!0!==R.get(t)&&R.set(t,!1)}},[j,x.length,x.join("-")]);let k=[];if(y!==C){let e=[...y];for(let t=0;t<j.length;t++){let n=j[t],r=m(n);x.includes(r)||(e.splice(t,0,n),k.push(n))}"wait"===l&&k.length&&(e=k),O(p(e)),L(y);return}let{forceRender:I}=(0,i.useContext)(o.p);return(0,r.jsx)(r.Fragment,{children:j.map(e=>{let t=m(e),i=(!f||!!g)&&(y===j||x.includes(t));return(0,r.jsx)(d,{isPresent:i,initial:(!w.current||!!c)&&void 0,custom:i?void 0:n,presenceAffectsLayout:u,mode:l,onExitComplete:i?void 0:()=>{if(!R.has(t))return;R.set(t,!0);let e=!0;R.forEach(t=>{t||(e=!1)}),e&&(null==I||I(),O(E.current),f&&(null==b||b()),a&&a())},children:e},t)})})}},6044:function(e,t,n){n.d(t,{YD:function(){return u}});var r=n(2265),i=Object.defineProperty,o=new Map,s=new WeakMap,c=0,a=void 0;function u(){var e;let{threshold:t,delay:n,trackVisibility:i,rootMargin:u,root:l,triggerOnce:d,skip:f,initialInView:h,fallbackInView:m,onChange:p}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[v,g]=r.useState(null),b=r.useRef(p),[y,x]=r.useState({inView:!!h,entry:void 0});b.current=p,r.useEffect(()=>{let e;if(!f&&v)return e=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:a;if(void 0===window.IntersectionObserver&&void 0!==r){let i=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:"number"==typeof n.threshold?n.threshold:0,time:0,boundingClientRect:i,intersectionRect:i,rootBounds:i}),()=>{}}let{id:i,observer:u,elements:l}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var n;return"".concat(t,"_").concat("root"===t?(n=e.root)?(s.has(n)||(c+=1,s.set(n,c.toString())),s.get(n)):"0":e[t])}).toString(),n=o.get(t);if(!n){let r;let i=new Map,s=new IntersectionObserver(t=>{t.forEach(t=>{var n;let o=t.isIntersecting&&r.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=o),null==(n=i.get(t.target))||n.forEach(e=>{e(o,t)})})},e);r=s.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:s,elements:i},o.set(t,n)}return n}(n),d=l.get(e)||[];return l.has(e)||l.set(e,d),d.push(t),u.observe(e),function(){d.splice(d.indexOf(t),1),0===d.length&&(l.delete(e),u.unobserve(e)),0===l.size&&(u.disconnect(),o.delete(i))}}(v,(t,n)=>{x({inView:t,entry:n}),b.current&&b.current(t,n),n.isIntersecting&&d&&e&&(e(),e=void 0)},{root:l,rootMargin:u,threshold:t,trackVisibility:i,delay:n},m),()=>{e&&e()}},[Array.isArray(t)?t.toString():t,v,l,u,d,f,i,m,n]);let w=null==(e=y.entry)?void 0:e.target,E=r.useRef(void 0);v||!w||d||f||E.current===w||(E.current=w,x({inView:!!h,entry:void 0}));let R=[g,y.inView,y.entry];return R.ref=R[0],R.inView=R[1],R.entry=R[2],R}r.Component}}]);