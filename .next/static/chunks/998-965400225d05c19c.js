(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[998],{2725:function(t,e){"use strict";var r,n;Object.defineProperty(e,"__esModule",{value:!0}),e.Doctype=e.CDATA=e.Tag=e.Style=e.Script=e.Comment=e.Directive=e.Text=e.Root=e.isTag=e.ElementType=void 0,(n=r=e.ElementType||(e.ElementType={})).Root="root",n.Text="text",n.Directive="directive",n.Comment="comment",n.Script="script",n.Style="style",n.Tag="tag",n.CDATA="cdata",n.Doctype="doctype",e.isTag=function(t){return t.type===r.Tag||t.type===r.Script||t.type===r.Style},e.Root=r.Root,e.Text=r.Text,e.Directive=r.Directive,e.Comment=r.Comment,e.Script=r.<PERSON>,e.Style=r.Style,e.Tag=r.Tag,e.CDATA=r.CDATA,e.Doctype=r.Doctype},8131:function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(e,r);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,i)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),i=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),e.DomHandler=void 0;var o=r(2725),s=r(9470);i(r(9470),e);var a={withStartIndices:!1,withEndIndices:!1,xmlMode:!1},l=function(){function t(t,e,r){this.dom=[],this.root=new s.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof e&&(r=e,e=a),"object"==typeof t&&(e=t,t=void 0),this.callback=null!=t?t:null,this.options=null!=e?e:a,this.elementCB=null!=r?r:null}return t.prototype.onparserinit=function(t){this.parser=t},t.prototype.onreset=function(){this.dom=[],this.root=new s.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},t.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},t.prototype.onerror=function(t){this.handleCallback(t)},t.prototype.onclosetag=function(){this.lastNode=null;var t=this.tagStack.pop();this.options.withEndIndices&&(t.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(t)},t.prototype.onopentag=function(t,e){var r=this.options.xmlMode?o.ElementType.Tag:void 0,n=new s.Element(t,e,void 0,r);this.addNode(n),this.tagStack.push(n)},t.prototype.ontext=function(t){var e=this.lastNode;if(e&&e.type===o.ElementType.Text)e.data+=t,this.options.withEndIndices&&(e.endIndex=this.parser.endIndex);else{var r=new s.Text(t);this.addNode(r),this.lastNode=r}},t.prototype.oncomment=function(t){if(this.lastNode&&this.lastNode.type===o.ElementType.Comment){this.lastNode.data+=t;return}var e=new s.Comment(t);this.addNode(e),this.lastNode=e},t.prototype.oncommentend=function(){this.lastNode=null},t.prototype.oncdatastart=function(){var t=new s.Text(""),e=new s.CDATA([t]);this.addNode(e),t.parent=e,this.lastNode=t},t.prototype.oncdataend=function(){this.lastNode=null},t.prototype.onprocessinginstruction=function(t,e){var r=new s.ProcessingInstruction(t,e);this.addNode(r)},t.prototype.handleCallback=function(t){if("function"==typeof this.callback)this.callback(t,this.dom);else if(t)throw t},t.prototype.addNode=function(t){var e=this.tagStack[this.tagStack.length-1],r=e.children[e.children.length-1];this.options.withStartIndices&&(t.startIndex=this.parser.startIndex),this.options.withEndIndices&&(t.endIndex=this.parser.endIndex),e.children.push(t),r&&(t.prev=r,r.next=t),t.parent=e,this.lastNode=null},t}();e.DomHandler=l,e.default=l},9470:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.cloneNode=e.hasChildren=e.isDocument=e.isDirective=e.isComment=e.isText=e.isCDATA=e.isTag=e.Element=e.Document=e.CDATA=e.NodeWithChildren=e.ProcessingInstruction=e.Comment=e.Text=e.DataNode=e.Node=void 0;var s=r(2725),a=function(){function t(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(t.prototype,"parentNode",{get:function(){return this.parent},set:function(t){this.parent=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"previousSibling",{get:function(){return this.prev},set:function(t){this.prev=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"nextSibling",{get:function(){return this.next},set:function(t){this.next=t},enumerable:!1,configurable:!0}),t.prototype.cloneNode=function(t){return void 0===t&&(t=!1),T(this,t)},t}();e.Node=a;var l=function(t){function e(e){var r=t.call(this)||this;return r.data=e,r}return i(e,t),Object.defineProperty(e.prototype,"nodeValue",{get:function(){return this.data},set:function(t){this.data=t},enumerable:!1,configurable:!0}),e}(a);e.DataNode=l;var u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=s.ElementType.Text,e}return i(e,t),Object.defineProperty(e.prototype,"nodeType",{get:function(){return 3},enumerable:!1,configurable:!0}),e}(l);e.Text=u;var c=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=s.ElementType.Comment,e}return i(e,t),Object.defineProperty(e.prototype,"nodeType",{get:function(){return 8},enumerable:!1,configurable:!0}),e}(l);e.Comment=c;var h=function(t){function e(e,r){var n=t.call(this,r)||this;return n.name=e,n.type=s.ElementType.Directive,n}return i(e,t),Object.defineProperty(e.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),e}(l);e.ProcessingInstruction=h;var d=function(t){function e(e){var r=t.call(this)||this;return r.children=e,r}return i(e,t),Object.defineProperty(e.prototype,"firstChild",{get:function(){var t;return null!==(t=this.children[0])&&void 0!==t?t:null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"childNodes",{get:function(){return this.children},set:function(t){this.children=t},enumerable:!1,configurable:!0}),e}(a);e.NodeWithChildren=d;var f=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=s.ElementType.CDATA,e}return i(e,t),Object.defineProperty(e.prototype,"nodeType",{get:function(){return 4},enumerable:!1,configurable:!0}),e}(d);e.CDATA=f;var p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=s.ElementType.Root,e}return i(e,t),Object.defineProperty(e.prototype,"nodeType",{get:function(){return 9},enumerable:!1,configurable:!0}),e}(d);e.Document=p;var m=function(t){function e(e,r,n,i){void 0===n&&(n=[]),void 0===i&&(i="script"===e?s.ElementType.Script:"style"===e?s.ElementType.Style:s.ElementType.Tag);var o=t.call(this,n)||this;return o.name=e,o.attribs=r,o.type=i,o}return i(e,t),Object.defineProperty(e.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tagName",{get:function(){return this.name},set:function(t){this.name=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"attributes",{get:function(){var t=this;return Object.keys(this.attribs).map(function(e){var r,n;return{name:e,value:t.attribs[e],namespace:null===(r=t["x-attribsNamespace"])||void 0===r?void 0:r[e],prefix:null===(n=t["x-attribsPrefix"])||void 0===n?void 0:n[e]}})},enumerable:!1,configurable:!0}),e}(d);function g(t){return(0,s.isTag)(t)}function v(t){return t.type===s.ElementType.CDATA}function y(t){return t.type===s.ElementType.Text}function b(t){return t.type===s.ElementType.Comment}function x(t){return t.type===s.ElementType.Directive}function w(t){return t.type===s.ElementType.Root}function T(t,e){if(void 0===e&&(e=!1),y(t))r=new u(t.data);else if(b(t))r=new c(t.data);else if(g(t)){var r,n=e?S(t.children):[],i=new m(t.name,o({},t.attribs),n);n.forEach(function(t){return t.parent=i}),null!=t.namespace&&(i.namespace=t.namespace),t["x-attribsNamespace"]&&(i["x-attribsNamespace"]=o({},t["x-attribsNamespace"])),t["x-attribsPrefix"]&&(i["x-attribsPrefix"]=o({},t["x-attribsPrefix"])),r=i}else if(v(t)){var n=e?S(t.children):[],s=new f(n);n.forEach(function(t){return t.parent=s}),r=s}else if(w(t)){var n=e?S(t.children):[],a=new p(n);n.forEach(function(t){return t.parent=a}),t["x-mode"]&&(a["x-mode"]=t["x-mode"]),r=a}else if(x(t)){var l=new h(t.name,t.data);null!=t["x-name"]&&(l["x-name"]=t["x-name"],l["x-publicId"]=t["x-publicId"],l["x-systemId"]=t["x-systemId"]),r=l}else throw Error("Not implemented yet: ".concat(t.type));return r.startIndex=t.startIndex,r.endIndex=t.endIndex,null!=t.sourceCodeLocation&&(r.sourceCodeLocation=t.sourceCodeLocation),r}function S(t){for(var e=t.map(function(t){return T(t,!0)}),r=1;r<e.length;r++)e[r].prev=e[r-1],e[r-1].next=e[r];return e}e.Element=m,e.isTag=g,e.isCDATA=v,e.isText=y,e.isComment=b,e.isDirective=x,e.isDocument=w,e.hasChildren=function(t){return Object.prototype.hasOwnProperty.call(t,"children")},e.cloneNode=T},6098:function(t,e){var r,n,i,o,s,a,l,u,c,h,d,f,p,m,g,v,y,b,x,w,T,S,E,A,P,C,k,R,_,M,O,D,j,L,I,V,B,F,N,U,z,W,H,Y,X,$,q,G,K,Z,J,Q,tt,te,tr,tn,ti,to,ts,ta,tl,tu,tc,th,td,tf,tp,tm,tg,tv,ty,tb,tx,tw,tT,tS,tE,tA,tP,tC,tk,tR,t_,tM,tO,tD,tj,tL,tI,tV,tB,tF,tN,tU,tz,tW,tH,tY,tX,t$,tq,tG,tK,tZ,tJ,tQ,t0,t1,t2,t5,t3,t6,t8,t4,t9,t7,et,ee,er,en,ei,eo,es,ea,el,eu,ec,eh,ed,ef,ep,em,eg,ev,ey,eb,ex,ew,eT,eS,eE,eA,eP,eC,ek,eR,e_,eM,eO,eD,ej,eL,eI,eV,eB,eF,eN,eU,ez,eW,eH,eY,eX,e$,eq,eG,eK,eZ,eJ,eQ,e0,e1,e2,e5,e3,e6,e8,e4,e9;m=function(){return r||"undefined"!=typeof window&&(r=window.gsap)&&r.registerPlugin&&r},g=1,v=[],y=[],b=[],x=Date.now,w=function(t,e){return e},T=function(){var t=c.core,e=t.bridge||{},r=t._scrollers,n=t._proxies;r.push.apply(r,y),n.push.apply(n,b),y=r,b=n,w=function(t,r){return e[t](r)}},S=function(t,e){return~b.indexOf(t)&&b[b.indexOf(t)+1][e]},E=function(t){return!!~h.indexOf(t)},A=function(t,e,r,n,i){return t.addEventListener(e,r,{passive:!1!==n,capture:!!i})},P=function(t,e,r,n){return t.removeEventListener(e,r,!!n)},C="scrollLeft",k="scrollTop",R=function(){return d&&d.isPressed||y.cache++},O={s:k,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:M={s:C,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:(_=function(t,e){var r=function r(n){if(n||0===n){g&&(i.history.scrollRestoration="manual");var o=d&&d.isPressed;t(n=r.v=Math.round(n)||(d&&d.iOS?1:0)),r.cacheID=y.cache,o&&w("ss",n)}else(e||y.cache!==r.cacheID||w("ref"))&&(r.cacheID=y.cache,r.v=t());return r.v+r.offset};return r.offset=0,t&&r})(function(t){return arguments.length?i.scrollTo(t,O.sc()):i.pageXOffset||o[C]||s[C]||a[C]||0})},sc:_(function(t){return arguments.length?i.scrollTo(M.sc(),t):i.pageYOffset||o[k]||s[k]||a[k]||0})},D=function(t,e){return(e&&e._ctx&&e._ctx.selector||r.utils.toArray)(t)[0]||("string"==typeof t&&!1!==r.config().nullTargetWarn?console.warn("Element not found:",t):null)},j=function(t,e){var n=e.s,i=e.sc;E(t)&&(t=o.scrollingElement||s);var a=y.indexOf(t),l=i===O.sc?1:2;~a||(a=y.push(t)-1),y[a+l]||A(t,"scroll",R);var u=y[a+l],c=u||(y[a+l]=_(S(t,n),!0)||(E(t)?i:_(function(e){return arguments.length?t[n]=e:t[n]})));return c.target=t,u||(c.smooth="smooth"===r.getProperty(t,"scrollBehavior")),c},L=function(t,e,r){var n=t,i=t,o=x(),s=o,a=e||50,l=Math.max(500,3*a),u=function(t,e){var l=x();e||l-o>a?(i=n,n=t,s=o,o=l):r?n+=t:n=i+(t-i)/(l-s)*(o-s)};return{update:u,reset:function(){i=n=r?0:n,s=o=0},getVelocity:function(t){var e=s,a=i,c=x();return(t||0===t)&&t!==n&&u(t),o===s||c-s>l?0:(n+(r?a:-a))/((r?c:o)-e)*1e3}}},I=function(t,e){return e&&!t._gsapAllow&&t.preventDefault(),t.changedTouches?t.changedTouches[0]:t},V=function(t){var e=Math.max.apply(Math,t),r=Math.min.apply(Math,t);return Math.abs(e)>=Math.abs(r)?e:r},B=function(){(c=r.core.globals().ScrollTrigger)&&c.core&&T()},F=function(t){return r=t||m(),!n&&r&&"undefined"!=typeof document&&document.body&&(i=window,s=(o=document).documentElement,a=o.body,h=[i,o,s,a],r.utils.clamp,p=r.core.context||function(){},u="onpointerenter"in a?"pointer":"mouse",l=N.isTouch=i.matchMedia&&i.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in i||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?2:0,f=N.eventTypes=("ontouchstart"in s?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in s?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return g=0},500),B(),n=1),n},M.op=O,y.cache=0,(N=function(){function t(t){this.init(t)}return t.prototype.init=function(t){n||F(r)||console.warn("Please gsap.registerPlugin(Observer)"),c||B();var e=t.tolerance,h=t.dragMinimum,m=t.type,g=t.target,y=t.lineHeight,b=t.debounce,w=t.preventDefault,T=t.onStop,S=t.onStopDelay,C=t.ignore,k=t.wheelSpeed,_=t.event,N=t.onDragStart,U=t.onDragEnd,z=t.onDrag,W=t.onPress,H=t.onRelease,Y=t.onRight,X=t.onLeft,$=t.onUp,q=t.onDown,G=t.onChangeX,K=t.onChangeY,Z=t.onChange,J=t.onToggleX,Q=t.onToggleY,tt=t.onHover,te=t.onHoverEnd,tr=t.onMove,tn=t.ignoreCheck,ti=t.isNormalizer,to=t.onGestureStart,ts=t.onGestureEnd,ta=t.onWheel,tl=t.onEnable,tu=t.onDisable,tc=t.onClick,th=t.scrollSpeed,td=t.capture,tf=t.allowClicks,tp=t.lockAxis,tm=t.onLockAxis;this.target=g=D(g)||s,this.vars=t,C&&(C=r.utils.toArray(C)),e=e||1e-9,h=h||0,k=k||1,th=th||1,m=m||"wheel,touch,pointer",b=!1!==b,y||(y=parseFloat(i.getComputedStyle(a).lineHeight)||22);var tg,tv,ty,tb,tx,tw,tT,tS=this,tE=0,tA=0,tP=t.passive||!w&&!1!==t.passive,tC=j(g,M),tk=j(g,O),tR=tC(),t_=tk(),tM=~m.indexOf("touch")&&!~m.indexOf("pointer")&&"pointerdown"===f[0],tO=E(g),tD=g.ownerDocument||o,tj=[0,0,0],tL=[0,0,0],tI=0,tV=function(){return tI=x()},tB=function(t,e){return(tS.event=t)&&C&&~C.indexOf(t.target)||e&&tM&&"touch"!==t.pointerType||tn&&tn(t,e)},tF=function(){var t=tS.deltaX=V(tj),r=tS.deltaY=V(tL),n=Math.abs(t)>=e,i=Math.abs(r)>=e;Z&&(n||i)&&Z(tS,t,r,tj,tL),n&&(Y&&tS.deltaX>0&&Y(tS),X&&tS.deltaX<0&&X(tS),G&&G(tS),J&&tS.deltaX<0!=tE<0&&J(tS),tE=tS.deltaX,tj[0]=tj[1]=tj[2]=0),i&&(q&&tS.deltaY>0&&q(tS),$&&tS.deltaY<0&&$(tS),K&&K(tS),Q&&tS.deltaY<0!=tA<0&&Q(tS),tA=tS.deltaY,tL[0]=tL[1]=tL[2]=0),(tb||ty)&&(tr&&tr(tS),ty&&(N&&1===ty&&N(tS),z&&z(tS),ty=0),tb=!1),tw&&(tw=!1,1)&&tm&&tm(tS),tx&&(ta(tS),tx=!1),tg=0},tN=function(t,e,r){tj[r]+=t,tL[r]+=e,tS._vx.update(t),tS._vy.update(e),b?tg||(tg=requestAnimationFrame(tF)):tF()},tU=function(t,e){tp&&!tT&&(tS.axis=tT=Math.abs(t)>Math.abs(e)?"x":"y",tw=!0),"y"!==tT&&(tj[2]+=t,tS._vx.update(t,!0)),"x"!==tT&&(tL[2]+=e,tS._vy.update(e,!0)),b?tg||(tg=requestAnimationFrame(tF)):tF()},tz=function(t){if(!tB(t,1)){var e=(t=I(t,w)).clientX,r=t.clientY,n=e-tS.x,i=r-tS.y,o=tS.isDragging;tS.x=e,tS.y=r,(o||(n||i)&&(Math.abs(tS.startX-e)>=h||Math.abs(tS.startY-r)>=h))&&(ty=o?2:1,o||(tS.isDragging=!0),tU(n,i))}},tW=tS.onPress=function(t){tB(t,1)||t&&t.button||(tS.axis=tT=null,tv.pause(),tS.isPressed=!0,t=I(t),tE=tA=0,tS.startX=tS.x=t.clientX,tS.startY=tS.y=t.clientY,tS._vx.reset(),tS._vy.reset(),A(ti?g:tD,f[1],tz,tP,!0),tS.deltaX=tS.deltaY=0,W&&W(tS))},tH=tS.onRelease=function(t){if(!tB(t,1)){P(ti?g:tD,f[1],tz,!0);var e=!isNaN(tS.y-tS.startY),n=tS.isDragging,o=n&&(Math.abs(tS.x-tS.startX)>3||Math.abs(tS.y-tS.startY)>3),s=I(t);!o&&e&&(tS._vx.reset(),tS._vy.reset(),w&&tf&&r.delayedCall(.08,function(){if(x()-tI>300&&!t.defaultPrevented){if(t.target.click)t.target.click();else if(tD.createEvent){var e=tD.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,i,1,s.screenX,s.screenY,s.clientX,s.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(e)}}})),tS.isDragging=tS.isGesturing=tS.isPressed=!1,T&&n&&!ti&&tv.restart(!0),ty&&tF(),U&&n&&U(tS),H&&H(tS,o)}},tY=function(t){return t.touches&&t.touches.length>1&&(tS.isGesturing=!0)&&to(t,tS.isDragging)},tX=function(){return tS.isGesturing=!1,ts(tS)},t$=function(t){if(!tB(t)){var e=tC(),r=tk();tN((e-tR)*th,(r-t_)*th,1),tR=e,t_=r,T&&tv.restart(!0)}},tq=function(t){if(!tB(t)){t=I(t,w),ta&&(tx=!0);var e=(1===t.deltaMode?y:2===t.deltaMode?i.innerHeight:1)*k;tN(t.deltaX*e,t.deltaY*e,0),T&&!ti&&tv.restart(!0)}},tG=function(t){if(!tB(t)){var e=t.clientX,r=t.clientY,n=e-tS.x,i=r-tS.y;tS.x=e,tS.y=r,tb=!0,T&&tv.restart(!0),(n||i)&&tU(n,i)}},tK=function(t){tS.event=t,tt(tS)},tZ=function(t){tS.event=t,te(tS)},tJ=function(t){return tB(t)||I(t,w)&&tc(tS)};tv=tS._dc=r.delayedCall(S||.25,function(){tS._vx.reset(),tS._vy.reset(),tv.pause(),T&&T(tS)}).pause(),tS.deltaX=tS.deltaY=0,tS._vx=L(0,50,!0),tS._vy=L(0,50,!0),tS.scrollX=tC,tS.scrollY=tk,tS.isDragging=tS.isGesturing=tS.isPressed=!1,p(this),tS.enable=function(t){return!tS.isEnabled&&(A(tO?tD:g,"scroll",R),m.indexOf("scroll")>=0&&A(tO?tD:g,"scroll",t$,tP,td),m.indexOf("wheel")>=0&&A(g,"wheel",tq,tP,td),(m.indexOf("touch")>=0&&l||m.indexOf("pointer")>=0)&&(A(g,f[0],tW,tP,td),A(tD,f[2],tH),A(tD,f[3],tH),tf&&A(g,"click",tV,!0,!0),tc&&A(g,"click",tJ),to&&A(tD,"gesturestart",tY),ts&&A(tD,"gestureend",tX),tt&&A(g,u+"enter",tK),te&&A(g,u+"leave",tZ),tr&&A(g,u+"move",tG)),tS.isEnabled=!0,tS.isDragging=tS.isGesturing=tS.isPressed=tb=ty=!1,tS._vx.reset(),tS._vy.reset(),tR=tC(),t_=tk(),t&&t.type&&tW(t),tl&&tl(tS)),tS},tS.disable=function(){tS.isEnabled&&(v.filter(function(t){return t!==tS&&E(t.target)}).length||P(tO?tD:g,"scroll",R),tS.isPressed&&(tS._vx.reset(),tS._vy.reset(),P(ti?g:tD,f[1],tz,!0)),P(tO?tD:g,"scroll",t$,td),P(g,"wheel",tq,td),P(g,f[0],tW,td),P(tD,f[2],tH),P(tD,f[3],tH),P(g,"click",tV,!0),P(g,"click",tJ),P(tD,"gesturestart",tY),P(tD,"gestureend",tX),P(g,u+"enter",tK),P(g,u+"leave",tZ),P(g,u+"move",tG),tS.isEnabled=tS.isPressed=tS.isDragging=!1,tu&&tu(tS))},tS.kill=tS.revert=function(){tS.disable();var t=v.indexOf(tS);t>=0&&v.splice(t,1),d===tS&&(d=0)},v.push(tS),ti&&E(g)&&(d=tS),tS.enable(_)},function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}(t.prototype,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),t}()).version="3.12.7",N.create=function(t){return new N(t)},N.register=F,N.getAll=function(){return v.slice()},N.getById=function(t){return v.filter(function(e){return e.vars.id===t})[0]},m()&&r.registerPlugin(N),tA=1,tC=(tP=Date.now)(),tk=0,tR=0,t_=function(t,e,r){var n=tH(t)&&("clamp("===t.substr(0,6)||t.indexOf("max")>-1);return r["_"+e+"Clamp"]=n,n?t.substr(6,t.length-7):t},tM=function(t,e){return e&&(!tH(t)||"clamp("!==t.substr(0,6))?"clamp("+t+")":t},tO=function(){return tt=1},tD=function(){return tt=0},tj=function(t){return t},tL=function(t){return Math.round(1e5*t)/1e5||0},tI=function(){return"undefined"!=typeof window},tV=function(){return U||tI()&&(U=window.gsap)&&U.registerPlugin&&U},tB=function(t){return!!~$.indexOf(t)},tF=function(t){return("Height"===t?tv:W["inner"+t])||Y["client"+t]||X["client"+t]},tN=function(t){return S(t,"getBoundingClientRect")||(tB(t)?function(){return eX.width=W.innerWidth,eX.height=tv,eX}:function(){return er(t)})},tU=function(t,e,r){var n=r.d,i=r.d2,o=r.a;return(o=S(t,"getBoundingClientRect"))?function(){return o()[n]}:function(){return(e?tF(i):t["client"+i])||0}},tz=function(t,e){var r=e.s,n=e.d2,i=e.d,o=e.a;return Math.max(0,(o=S(t,r="scroll"+n))?o()-tN(t)()[i]:tB(t)?(Y[r]||X[r])-tF(n):t[r]-t["offset"+n])},tW=function(t,e){for(var r=0;r<to.length;r+=3)(!e||~e.indexOf(to[r+1]))&&t(to[r],to[r+1],to[r+2])},tH=function(t){return"string"==typeof t},tY=function(t){return"function"==typeof t},tX=function(t){return"number"==typeof t},t$=function(t){return"object"==typeof t},tq=function(t,e,r){return t&&t.progress(e?0:1)&&r&&t.pause()},tG=function(t,e){if(t.enabled){var r=t._ctx?t._ctx.add(function(){return e(t)}):e(t);r&&r.totalTime&&(t.callbackAnimation=r)}},tK=Math.abs,tZ="left",tJ="right",tQ="bottom",t0="width",t1="height",t2="Right",t5="Left",t3="Bottom",t6="padding",t8="margin",t4="Width",t9="Height",t7=function(t){return W.getComputedStyle(t)},et=function(t){var e=t7(t).position;t.style.position="absolute"===e||"fixed"===e?e:"relative"},ee=function(t,e){for(var r in e)r in t||(t[r]=e[r]);return t},er=function(t,e){var r=e&&"matrix(1, 0, 0, 1, 0, 0)"!==t7(t)[te]&&U.to(t,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=t.getBoundingClientRect();return r&&r.progress(0).kill(),n},en=function(t,e){var r=e.d2;return t["offset"+r]||t["client"+r]||0},ei=function(t){var e,r=[],n=t.labels,i=t.duration();for(e in n)r.push(n[e]/i);return r},eo=function(t){var e=U.utils.snap(t),r=Array.isArray(t)&&t.slice(0).sort(function(t,e){return t-e});return r?function(t,n,i){var o;if(void 0===i&&(i=.001),!n)return e(t);if(n>0){for(t-=i,o=0;o<r.length;o++)if(r[o]>=t)return r[o];return r[o-1]}for(o=r.length,t+=i;o--;)if(r[o]<=t)return r[o];return r[0]}:function(r,n,i){void 0===i&&(i=.001);var o=e(r);return!n||Math.abs(o-r)<i||o-r<0==n<0?o:e(n<0?r-t:r+t)}},es=function(t,e,r,n){return r.split(",").forEach(function(r){return t(e,r,n)})},ea=function(t,e,r,n,i){return t.addEventListener(e,r,{passive:!n,capture:!!i})},el=function(t,e,r,n){return t.removeEventListener(e,r,!!n)},eu=function(t,e,r){(r=r&&r.wheelHandler)&&(t(e,"wheel",r),t(e,"touchmove",r))},ec={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},eh={toggleActions:"play",anticipatePin:0},ed={top:0,left:0,center:.5,bottom:1,right:1},ef=function(t,e){if(tH(t)){var r=t.indexOf("="),n=~r?+(t.charAt(r-1)+1)*parseFloat(t.substr(r+1)):0;~r&&(t.indexOf("%")>r&&(n*=e/100),t=t.substr(0,r-1)),t=n+(t in ed?ed[t]*e:~t.indexOf("%")?parseFloat(t)*e/100:parseFloat(t)||0)}return t},ep=function(t,e,r,n,i,o,s,a){var l=i.startColor,u=i.endColor,c=i.fontSize,h=i.indent,d=i.fontWeight,f=H.createElement("div"),p=tB(r)||"fixed"===S(r,"pinType"),m=-1!==t.indexOf("scroller"),g=p?X:r,v=-1!==t.indexOf("start"),y=v?l:u,b="border-color:"+y+";font-size:"+c+";color:"+y+";font-weight:"+d+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return b+="position:"+((m||a)&&p?"fixed;":"absolute;"),(m||a||!p)&&(b+=(n===O?tJ:tQ)+":"+(o+parseFloat(h))+"px;"),s&&(b+="box-sizing:border-box;text-align:left;width:"+s.offsetWidth+"px;"),f._isStart=v,f.setAttribute("class","gsap-marker-"+t+(e?" marker-"+e:"")),f.style.cssText=b,f.innerText=e||0===e?t+"-"+e:t,g.children[0]?g.insertBefore(f,g.children[0]):g.appendChild(f),f._offset=f["offset"+n.op.d2],em(f,0,n,v),f},em=function(t,e,r,n){var i={display:"block"},o=r[n?"os2":"p2"],s=r[n?"p2":"os2"];t._isFlipped=n,i[r.a+"Percent"]=n?-100:0,i[r.a]=n?"1px":0,i["border"+o+t4]=1,i["border"+s+t4]=0,i[r.p]=e+"px",U.set(t,i)},eg=[],ev={},ey=function(){return tP()-tk>34&&(tw||(tw=requestAnimationFrame(eV)))},eb=function(){tu&&tu.isPressed&&!(tu.startX>X.clientWidth)||(y.cache++,tu?tw||(tw=requestAnimationFrame(eV)):eV(),tk||eA("scrollStart"),tk=tP())},ex=function(){td=W.innerWidth,th=W.innerHeight},ew=function(t){y.cache++,(!0===t||!Q&&!tl&&!H.fullscreenElement&&!H.webkitFullscreenElement&&(!tc||td!==W.innerWidth||Math.abs(W.innerHeight-th)>.25*W.innerHeight))&&q.restart(!0)},eT={},eS=[],eE=function t(){return el(eQ,"scrollEnd",t)||ej(!0)},eA=function(t){return eT[t]&&eT[t].map(function(t){return t()})||eS},eP=[],eC=function(t){for(var e=0;e<eP.length;e+=5)(!t||eP[e+4]&&eP[e+4].query===t)&&(eP[e].style.cssText=eP[e+1],eP[e].getBBox&&eP[e].setAttribute("transform",eP[e+2]||""),eP[e+3].uncache=1)},ek=function(t,e){var r;for(tr=0;tr<eg.length;tr++)(r=eg[tr])&&(!e||r._ctx===e)&&(t?r.kill(1):r.revert(!0,!0));ty=!0,e&&eC(e),e||eA("revert")},eR=function(t,e){y.cache++,(e||!tT)&&y.forEach(function(t){return tY(t)&&t.cacheID++&&(t.rec=0)}),tH(t)&&(W.history.scrollRestoration=tm=t)},e_=0,eM=function(){if(tS!==e_){var t=tS=e_;requestAnimationFrame(function(){return t===e_&&ej(!0)})}},eO=function(){X.appendChild(tg),tv=!tu&&tg.offsetHeight||W.innerHeight,X.removeChild(tg)},eD=function(t){return G(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(e){return e.style.display=t?"none":"block"})},ej=function(t,e){if(Y=H.documentElement,X=H.body,$=[W,H,Y,X],tk&&!t&&!ty){ea(eQ,"scrollEnd",eE);return}eO(),tT=eQ.isRefreshing=!0,y.forEach(function(t){return tY(t)&&++t.cacheID&&(t.rec=t())});var r=eA("refreshInit");ts&&eQ.sort(),e||ek(),y.forEach(function(t){tY(t)&&(t.smooth&&(t.target.style.scrollBehavior="auto"),t(0))}),eg.slice(0).forEach(function(t){return t.refresh()}),ty=!1,eg.forEach(function(t){if(t._subPinOffset&&t.pin){var e=t.vars.horizontal?"offsetWidth":"offsetHeight",r=t.pin[e];t.revert(!0,1),t.adjustPinSpacing(t.pin[e]-r),t.refresh()}}),tb=1,eD(!0),eg.forEach(function(t){var e=tz(t.scroller,t._dir),r="max"===t.vars.end||t._endClamp&&t.end>e,n=t._startClamp&&t.start>=e;(r||n)&&t.setPositions(n?e-1:t.start,r?Math.max(n?e:t.start+1,e):t.end,!0)}),eD(!1),tb=0,r.forEach(function(t){return t&&t.render&&t.render(-1)}),y.forEach(function(t){tY(t)&&(t.smooth&&requestAnimationFrame(function(){return t.target.style.scrollBehavior="smooth"}),t.rec&&t(t.rec))}),eR(tm,1),q.pause(),e_++,tT=2,eV(2),eg.forEach(function(t){return tY(t.vars.onRefresh)&&t.vars.onRefresh(t)}),tT=eQ.isRefreshing=!1,eA("refresh")},eL=0,eI=1,eV=function(t){if(2===t||!tT&&!ty){eQ.isUpdating=!0,tE&&tE.update(0);var e=eg.length,r=tP(),n=r-tC>=50,i=e&&eg[0].scroll();if(eI=eL>i?-1:1,tT||(eL=i),n&&(tk&&!tt&&r-tk>200&&(tk=0,eA("scrollEnd")),Z=tC,tC=r),eI<0){for(tr=e;tr-- >0;)eg[tr]&&eg[tr].update(0,n);eI=1}else for(tr=0;tr<e;tr++)eg[tr]&&eg[tr].update(0,n);eQ.isUpdating=!1}tw=0},eF=(eB=[tZ,"top",tQ,tJ,t8+t3,t8+t2,t8+"Top",t8+t5,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"]).concat([t0,t1,"boxSizing","max"+t4,"max"+t9,"position",t8,t6,t6+"Top",t6+t2,t6+t3,t6+t5]),eN=function(t,e,r){eW(r);var n=t._gsap;if(n.spacerIsNative)eW(n.spacerState);else if(t._gsap.swappedIn){var i=e.parentNode;i&&(i.insertBefore(t,e),i.removeChild(e))}t._gsap.swappedIn=!1},eU=function(t,e,r,n){if(!t._gsap.swappedIn){for(var i,o=eB.length,s=e.style,a=t.style;o--;)s[i=eB[o]]=r[i];s.position="absolute"===r.position?"absolute":"relative","inline"===r.display&&(s.display="inline-block"),a[tQ]=a[tJ]="auto",s.flexBasis=r.flexBasis||"auto",s.overflow="visible",s.boxSizing="border-box",s[t0]=en(t,M)+"px",s[t1]=en(t,O)+"px",s[t6]=a[t8]=a.top=a[tZ]="0",eW(n),a[t0]=a["max"+t4]=r[t0],a[t1]=a["max"+t9]=r[t1],a[t6]=r[t6],t.parentNode!==e&&(t.parentNode.insertBefore(e,t),e.appendChild(t)),t._gsap.swappedIn=!0}},ez=/([A-Z])/g,eW=function(t){if(t){var e,r,n=t.t.style,i=t.length,o=0;for((t.t._gsap||U.core.getCache(t.t)).uncache=1;o<i;o+=2)r=t[o+1],e=t[o],r?n[e]=r:n[e]&&n.removeProperty(e.replace(ez,"-$1").toLowerCase())}},eH=function(t){for(var e=eF.length,r=t.style,n=[],i=0;i<e;i++)n.push(eF[i],r[eF[i]]);return n.t=t,n},eY=function(t,e,r){for(var n,i=[],o=t.length,s=r?8:0;s<o;s+=2)n=t[s],i.push(n,n in e?e[n]:t[s+1]);return i.t=t.t,i},eX={left:0,top:0},e$=function(t,e,r,n,i,o,s,a,l,u,c,h,d,f){tY(t)&&(t=t(a)),tH(t)&&"max"===t.substr(0,3)&&(t=h+("="===t.charAt(4)?ef("0"+t.substr(3),r):0));var p,m,g,v=d?d.time():0;if(d&&d.seek(0),isNaN(t)||(t=+t),tX(t))d&&(t=U.utils.mapRange(d.scrollTrigger.start,d.scrollTrigger.end,0,h,t)),s&&em(s,r,n,!0);else{tY(e)&&(e=e(a));var y,b,x,w,T=(t||"0").split(" ");(y=er(g=D(e,a)||X)||{}).left||y.top||"none"!==t7(g).display||(w=g.style.display,g.style.display="block",y=er(g),w?g.style.display=w:g.style.removeProperty("display")),b=ef(T[0],y[n.d]),x=ef(T[1]||"0",r),t=y[n.p]-l[n.p]-u+b+i-x,s&&em(s,x,n,r-x<20||s._isStart&&x>20),r-=r-x}if(f&&(a[f]=t||-.001,t<0&&(t=0)),o){var S=t+r,E=o._isStart;p="scroll"+n.d2,em(o,S,n,E&&S>20||!E&&(c?Math.max(X[p],Y[p]):o.parentNode[p])<=S+1),c&&(l=er(s),c&&(o.style[n.op.p]=l[n.op.p]-n.op.m-o._offset+"px"))}return d&&g&&(p=er(g),d.seek(h),m=er(g),d._caScrollDist=p[n.p]-m[n.p],t=t/d._caScrollDist*h),d&&d.seek(v),d?t:Math.round(t)},eq=/(webkit|moz|length|cssText|inset)/i,eG=function(t,e,r,n){if(t.parentNode!==e){var i,o,s=t.style;if(e===X){for(i in t._stOrig=s.cssText,o=t7(t))+i||eq.test(i)||!o[i]||"string"!=typeof s[i]||"0"===i||(s[i]=o[i]);s.top=r,s.left=n}else s.cssText=t._stOrig;U.core.getCache(t).uncache=1,e.appendChild(t)}},eK=function(t,e,r){var n=e,i=n;return function(e){var o=Math.round(t());return o!==n&&o!==i&&Math.abs(o-n)>3&&Math.abs(o-i)>3&&(e=o,r&&r()),i=n,n=Math.round(e)}},eZ=function(t,e,r){var n={};n[e.p]="+="+r,U.set(t,n)},eJ=function(t,e){var r=j(t,e),n="_scroll"+e.p2,i=function e(i,o,s,a,l){var u=e.tween,c=o.onComplete,h={};s=s||r();var d=eK(r,s,function(){u.kill(),e.tween=0});return l=a&&l||0,a=a||i-s,u&&u.kill(),o[n]=i,o.inherit=!1,o.modifiers=h,h[n]=function(){return d(s+a*u.ratio+l*u.ratio*u.ratio)},o.onUpdate=function(){y.cache++,e.tween&&eV()},o.onComplete=function(){e.tween=0,c&&c.call(u)},u=e.tween=U.to(t,o)};return t[n]=r,r.wheelHandler=function(){return i.tween&&i.tween.kill()&&(i.tween=0)},ea(t,"wheel",r.wheelHandler),eQ.isTouch&&ea(t,"touchmove",r.wheelHandler),i},(eQ=function(){function t(e,r){z||t.register(U)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),tp(this),this.init(e,r)}return t.prototype.init=function(e,r){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!tR){this.update=this.refresh=this.kill=tj;return}var n,i,o,s,a,l,u,c,h,d,f,p,m,g,v,x,w,T,E,A,P,C,k,R,_,L,I,V,B,F,N,z,$,q,J,te,tn,ti,to,tl,tu,tc=e=ee(tH(e)||tX(e)||e.nodeType?{trigger:e}:e,eh),th=tc.onUpdate,td=tc.toggleClass,tf=tc.id,tp=tc.onToggle,tm=tc.onRefresh,tg=tc.scrub,tv=tc.trigger,ty=tc.pin,tw=tc.pinSpacing,tS=tc.invalidateOnRefresh,tC=tc.anticipatePin,tO=tc.onScrubComplete,tD=tc.onSnapComplete,tI=tc.once,tV=tc.snap,tF=tc.pinReparent,tW=tc.pinSpacer,tZ=tc.containerAnimation,tJ=tc.fastScrollEnd,tQ=tc.preventOverlaps,es=e.horizontal||e.containerAnimation&&!1!==e.horizontal?M:O,eu=!tg&&0!==tg,ed=D(e.scroller||W),em=U.core.getCache(ed),ey=tB(ed),ex=("pinType"in e?e.pinType:S(ed,"pinType")||ey&&"fixed")==="fixed",eT=[e.onEnter,e.onLeave,e.onEnterBack,e.onLeaveBack],eS=eu&&e.toggleActions.split(" "),eA="markers"in e?e.markers:eh.markers,eP=ey?0:parseFloat(t7(ed)["border"+es.p2+t4])||0,eC=this,ek=e.onRefreshInit&&function(){return e.onRefreshInit(eC)},eR=tU(ed,ey,es),e_=!ey||~b.indexOf(ed)?tN(ed):function(){return eX},eO=0,eD=0,ej=0,eL=j(ed,es);if(eC._startClamp=eC._endClamp=!1,eC._dir=es,tC*=45,eC.scroller=ed,eC.scroll=tZ?tZ.time.bind(tZ):eL,l=eL(),eC.vars=e,r=r||e.animation,"refreshPriority"in e&&(ts=1,-9999===e.refreshPriority&&(tE=eC)),em.tweenScroll=em.tweenScroll||{top:eJ(ed,O),left:eJ(ed,M)},eC.tweenTo=o=em.tweenScroll[es.p],eC.scrubDuration=function(t){(J=tX(t)&&t)?q?q.duration(t):q=U.to(r,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:J,paused:!0,onComplete:function(){return tO&&tO(eC)}}):(q&&q.progress(1).kill(),q=0)},r&&(r.vars.lazy=!1,r._initted&&!eC.isReverted||!1!==r.vars.immediateRender&&!1!==e.immediateRender&&r.duration()&&r.render(0,!0,!0),eC.animation=r.pause(),r.scrollTrigger=eC,eC.scrubDuration(tg),z=0,tf||(tf=r.vars.id)),tV&&((!t$(tV)||tV.push)&&(tV={snapTo:tV}),"scrollBehavior"in X.style&&U.set(ey?[X,Y]:ed,{scrollBehavior:"auto"}),y.forEach(function(t){return tY(t)&&t.target===(ey?H.scrollingElement||Y:ed)&&(t.smooth=!1)}),a=tY(tV.snapTo)?tV.snapTo:"labels"===tV.snapTo?(n=r,function(t){return U.utils.snap(ei(n),t)}):"labelsDirectional"===tV.snapTo?(i=r,function(t,e){return eo(ei(i))(t,e.direction)}):!1!==tV.directional?function(t,e){return eo(tV.snapTo)(t,tP()-eD<500?0:e.direction)}:U.utils.snap(tV.snapTo),te=t$(te=tV.duration||{min:.1,max:2})?K(te.min,te.max):K(te,te),tn=U.delayedCall(tV.delay||J/2||.1,function(){var t=eL(),e=tP()-eD<500,n=o.tween;if((e||10>Math.abs(eC.getVelocity()))&&!n&&!tt&&eO!==t){var i,s,l=(t-c)/x,u=r&&!eu?r.totalProgress():l,d=e?0:(u-$)/(tP()-Z)*1e3||0,f=U.utils.clamp(-l,1-l,tK(d/2)*d/.185),p=l+(!1===tV.inertia?0:f),m=tV,g=m.onStart,v=m.onInterrupt,y=m.onComplete;if(tX(i=a(p,eC))||(i=p),s=Math.max(0,Math.round(c+i*x)),t<=h&&t>=c&&s!==t){if(n&&!n._initted&&n.data<=tK(s-t))return;!1===tV.inertia&&(f=i-l),o(s,{duration:te(tK(.185*Math.max(tK(p-u),tK(i-u))/d/.05||0)),ease:tV.ease||"power3",data:tK(s-t),onInterrupt:function(){return tn.restart(!0)&&v&&v(eC)},onComplete:function(){eC.update(),eO=eL(),r&&!eu&&(q?q.resetTo("totalProgress",i,r._tTime/r._tDur):r.progress(i)),z=$=r&&!eu?r.totalProgress():eC.progress,tD&&tD(eC),y&&y(eC)}},t,f*x,s-t-f*x),g&&g(eC,o.tween)}}else eC.isActive&&eO!==t&&tn.restart(!0)}).pause()),tf&&(ev[tf]=eC),(tu=(tv=eC.trigger=D(tv||!0!==ty&&ty))&&tv._gsap&&tv._gsap.stRevert)&&(tu=tu(eC)),ty=!0===ty?tv:D(ty),tH(td)&&(td={targets:tv,className:td}),ty&&(!1===tw||tw===t8||(tw=(!!tw||!ty.parentNode||!ty.parentNode.style||"flex"!==t7(ty.parentNode).display)&&t6),eC.pin=ty,(s=U.core.getCache(ty)).spacer?w=s.pinState:(tW&&((tW=D(tW))&&!tW.nodeType&&(tW=tW.current||tW.nativeElement),s.spacerIsNative=!!tW,tW&&(s.spacerState=eH(tW))),s.spacer=A=tW||H.createElement("div"),A.classList.add("pin-spacer"),tf&&A.classList.add("pin-spacer-"+tf),s.pinState=w=eH(ty)),!1!==e.force3D&&U.set(ty,{force3D:!0}),eC.spacer=A=s.spacer,L=(N=t7(ty))[tw+es.os2],C=U.getProperty(ty),k=U.quickSetter(ty,es.a,"px"),eU(ty,A,N),E=eH(ty)),eA){g=t$(eA)?ee(eA,ec):ec,p=ep("scroller-start",tf,ed,es,g,0),m=ep("scroller-end",tf,ed,es,g,0,p),P=p["offset"+es.op.d2];var eV=D(S(ed,"content")||ed);d=this.markerStart=ep("start",tf,eV,es,g,P,0,tZ),f=this.markerEnd=ep("end",tf,eV,es,g,P,0,tZ),tZ&&(tl=U.quickSetter([d,f],es.a,"px")),ex||b.length&&!0===S(ed,"fixedMarkers")||(et(ey?X:ed),U.set([p,m],{force3D:!0}),V=U.quickSetter(p,es.a,"px"),F=U.quickSetter(m,es.a,"px"))}if(tZ){var eB=tZ.vars.onUpdate,eF=tZ.vars.onUpdateParams;tZ.eventCallback("onUpdate",function(){eC.update(0,0,1),eB&&eB.apply(tZ,eF||[])})}if(eC.previous=function(){return eg[eg.indexOf(eC)-1]},eC.next=function(){return eg[eg.indexOf(eC)+1]},eC.revert=function(t,e){if(!e)return eC.kill(!0);var n=!1!==t||!eC.enabled,i=Q;n!==eC.isReverted&&(n&&(ti=Math.max(eL(),eC.scroll.rec||0),ej=eC.progress,to=r&&r.progress()),d&&[d,f,p,m].forEach(function(t){return t.style.display=n?"none":"block"}),n&&(Q=eC,eC.update(n)),!ty||tF&&eC.isActive||(n?eN(ty,A,w):eU(ty,A,t7(ty),I)),n||eC.update(n),Q=i,eC.isReverted=n)},eC.refresh=function(n,i,s,a){if(!Q&&eC.enabled||i){if(ty&&n&&tk){ea(t,"scrollEnd",eE);return}!tT&&ek&&ek(eC),Q=eC,o.tween&&!s&&(o.tween.kill(),o.tween=0),q&&q.pause(),tS&&r&&r.revert({kill:!1}).invalidate(),eC.isReverted||eC.revert(!0,!0),eC._subPinOffset=!1;var g,y,b,S,P,k,L,V,F,N,z,W,$,G=eR(),K=e_(),Z=tZ?tZ.duration():tz(ed,es),J=x<=.01,tt=0,te=a||0,tr=t$(s)?s.end:e.end,ts=e.endTrigger||tv,tl=t$(s)?s.start:e.start||(0!==e.start&&tv?ty?"0 0":"0 100%":0),tu=eC.pinnedContainer=e.pinnedContainer&&D(e.pinnedContainer,eC),tc=tv&&Math.max(0,eg.indexOf(eC))||0,th=tc;for(eA&&t$(s)&&(W=U.getProperty(p,es.p),$=U.getProperty(m,es.p));th-- >0;)(k=eg[th]).end||k.refresh(0,1)||(Q=eC),(L=k.pin)&&(L===tv||L===ty||L===tu)&&!k.isReverted&&(N||(N=[]),N.unshift(k),k.revert(!0,!0)),k!==eg[th]&&(tc--,th--);for(tY(tl)&&(tl=tl(eC)),c=e$(tl=t_(tl,"start",eC),tv,G,es,eL(),d,p,eC,K,eP,ex,Z,tZ,eC._startClamp&&"_startClamp")||(ty?-.001:0),tY(tr)&&(tr=tr(eC)),tH(tr)&&!tr.indexOf("+=")&&(~tr.indexOf(" ")?tr=(tH(tl)?tl.split(" ")[0]:"")+tr:(tt=ef(tr.substr(2),G),tr=tH(tl)?tl:(tZ?U.utils.mapRange(0,tZ.duration(),tZ.scrollTrigger.start,tZ.scrollTrigger.end,c):c)+tt,ts=tv)),tr=t_(tr,"end",eC),h=Math.max(c,e$(tr||(ts?"100% 0":Z),ts,G,es,eL()+tt,f,m,eC,K,eP,ex,Z,tZ,eC._endClamp&&"_endClamp"))||-.001,tt=0,th=tc;th--;)(L=(k=eg[th]).pin)&&k.start-k._pinPush<=c&&!tZ&&k.end>0&&(g=k.end-(eC._startClamp?Math.max(0,k.start):k.start),(L===tv&&k.start-k._pinPush<c||L===tu)&&isNaN(tl)&&(tt+=g*(1-k.progress)),L===ty&&(te+=g));if(c+=tt,h+=tt,eC._startClamp&&(eC._startClamp+=tt),eC._endClamp&&!tT&&(eC._endClamp=h||-.001,h=Math.min(h,tz(ed,es))),x=h-c||(c-=.01)&&.001,J&&(ej=U.utils.clamp(0,1,U.utils.normalize(c,h,ti))),eC._pinPush=te,d&&tt&&((g={})[es.a]="+="+tt,tu&&(g[es.p]="-="+eL()),U.set([d,f],g)),ty&&!(tb&&eC.end>=tz(ed,es)))g=t7(ty),S=es===O,b=eL(),R=parseFloat(C(es.a))+te,!Z&&h>1&&(z={style:z=(ey?H.scrollingElement||Y:ed).style,value:z["overflow"+es.a.toUpperCase()]},ey&&"scroll"!==t7(X)["overflow"+es.a.toUpperCase()]&&(z.style["overflow"+es.a.toUpperCase()]="scroll")),eU(ty,A,g),E=eH(ty),y=er(ty,!0),V=ex&&j(ed,S?M:O)(),tw?((I=[tw+es.os2,x+te+"px"]).t=A,(th=tw===t6?en(ty,es)+x+te:0)&&(I.push(es.d,th+"px"),"auto"!==A.style.flexBasis&&(A.style.flexBasis=th+"px")),eW(I),tu&&eg.forEach(function(t){t.pin===tu&&!1!==t.vars.pinSpacing&&(t._subPinOffset=!0)}),ex&&eL(ti)):(th=en(ty,es))&&"auto"!==A.style.flexBasis&&(A.style.flexBasis=th+"px"),ex&&((P={top:y.top+(S?b-c:V)+"px",left:y.left+(S?V:b-c)+"px",boxSizing:"border-box",position:"fixed"})[t0]=P["max"+t4]=Math.ceil(y.width)+"px",P[t1]=P["max"+t9]=Math.ceil(y.height)+"px",P[t8]=P[t8+"Top"]=P[t8+t2]=P[t8+t3]=P[t8+t5]="0",P[t6]=g[t6],P[t6+"Top"]=g[t6+"Top"],P[t6+t2]=g[t6+t2],P[t6+t3]=g[t6+t3],P[t6+t5]=g[t6+t5],T=eY(w,P,tF),tT&&eL(0)),r?(F=r._initted,ta(1),r.render(r.duration(),!0,!0),_=C(es.a)-R+x+te,B=Math.abs(x-_)>1,ex&&B&&T.splice(T.length-2,2),r.render(0,!0,!0),F||r.invalidate(!0),r.parent||r.totalTime(r.totalTime()),ta(0)):_=x,z&&(z.value?z.style["overflow"+es.a.toUpperCase()]=z.value:z.style.removeProperty("overflow-"+es.a));else if(tv&&eL()&&!tZ)for(y=tv.parentNode;y&&y!==X;)y._pinOffset&&(c-=y._pinOffset,h-=y._pinOffset),y=y.parentNode;N&&N.forEach(function(t){return t.revert(!1,!0)}),eC.start=c,eC.end=h,l=u=tT?ti:eL(),tZ||tT||(l<ti&&eL(ti),eC.scroll.rec=0),eC.revert(!1,!0),eD=tP(),tn&&(eO=-1,tn.restart(!0)),Q=0,r&&eu&&(r._initted||to)&&r.progress()!==to&&r.progress(to||0,!0).render(r.time(),!0,!0),(J||ej!==eC.progress||tZ||tS||r&&!r._initted)&&(r&&!eu&&r.totalProgress(tZ&&c<-.001&&!ej?U.utils.normalize(c,h,0):ej,!0),eC.progress=J||(l-c)/x===ej?0:ej),ty&&tw&&(A._pinOffset=Math.round(eC.progress*_)),q&&q.invalidate(),isNaN(W)||(W-=U.getProperty(p,es.p),$-=U.getProperty(m,es.p),eZ(p,es,W),eZ(d,es,W-(a||0)),eZ(m,es,$),eZ(f,es,$-(a||0))),J&&!tT&&eC.update(),!tm||tT||v||(v=!0,tm(eC),v=!1)}},eC.getVelocity=function(){return(eL()-u)/(tP()-Z)*1e3||0},eC.endAnimation=function(){tq(eC.callbackAnimation),r&&(q?q.progress(1):r.paused()?eu||tq(r,eC.direction<0,1):tq(r,r.reversed()))},eC.labelToScroll=function(t){return r&&r.labels&&(c||eC.refresh()||c)+r.labels[t]/r.duration()*x||0},eC.getTrailing=function(t){var e=eg.indexOf(eC),r=eC.direction>0?eg.slice(0,e).reverse():eg.slice(e+1);return(tH(t)?r.filter(function(e){return e.vars.preventOverlaps===t}):r).filter(function(t){return eC.direction>0?t.end<=c:t.start>=h})},eC.update=function(t,e,n){if(!tZ||n||t){var i,s,a,d,f,m,g,v=!0===tT?ti:eC.scroll(),y=t?0:(v-c)/x,b=y<0?0:y>1?1:y||0,w=eC.progress;if(e&&(u=l,l=tZ?eL():v,tV&&($=z,z=r&&!eu?r.totalProgress():b)),tC&&ty&&!Q&&!tA&&tk&&(!b&&c<v+(v-u)/(tP()-Z)*tC?b=1e-4:1===b&&h>v+(v-u)/(tP()-Z)*tC&&(b=.9999)),b!==w&&eC.enabled){if(d=(f=(i=eC.isActive=!!b&&b<1)!=(!!w&&w<1))||!!b!=!!w,eC.direction=b>w?1:-1,eC.progress=b,d&&!Q&&(s=b&&!w?0:1===b?1:1===w?2:3,eu&&(a=!f&&"none"!==eS[s+1]&&eS[s+1]||eS[s],g=r&&("complete"===a||"reset"===a||a in r))),tQ&&(f||g)&&(g||tg||!r)&&(tY(tQ)?tQ(eC):eC.getTrailing(tQ).forEach(function(t){return t.endAnimation()})),!eu&&(!q||Q||tA?r&&r.totalProgress(b,!!(Q&&(eD||t))):(q._dp._time-q._start!==q._time&&q.render(q._dp._time-q._start),q.resetTo?q.resetTo("totalProgress",b,r._tTime/r._tDur):(q.vars.totalProgress=b,q.invalidate().restart()))),ty){if(t&&tw&&(A.style[tw+es.os2]=L),ex){if(d){if(m=!t&&b>w&&h+1>v&&v+1>=tz(ed,es),tF){if(!t&&(i||m)){var S=er(ty,!0),P=v-c;eG(ty,X,S.top+(es===O?P:0)+"px",S.left+(es===O?0:P)+"px")}else eG(ty,A)}eW(i||m?T:E),B&&b<1&&i||k(R+(1!==b||m?0:_))}}else k(tL(R+_*b))}!tV||o.tween||Q||tA||tn.restart(!0),td&&(f||tI&&b&&(b<1||!tx))&&G(td.targets).forEach(function(t){return t.classList[i||tI?"add":"remove"](td.className)}),!th||eu||t||th(eC),d&&!Q?(eu&&(g&&("complete"===a?r.pause().totalProgress(1):"reset"===a?r.restart(!0).pause():"restart"===a?r.restart(!0):r[a]()),th&&th(eC)),(f||!tx)&&(tp&&f&&tG(eC,tp),eT[s]&&tG(eC,eT[s]),tI&&(1===b?eC.kill(!1,1):eT[s]=0),!f&&eT[s=1===b?1:3]&&tG(eC,eT[s])),tJ&&!i&&Math.abs(eC.getVelocity())>(tX(tJ)?tJ:2500)&&(tq(eC.callbackAnimation),q?q.progress(1):tq(r,"reverse"===a?1:!b,1))):eu&&th&&!Q&&th(eC)}if(F){var C=tZ?v/tZ.duration()*(tZ._caScrollDist||0):v;V(C+(p._isFlipped?1:0)),F(C)}tl&&tl(-v/tZ.duration()*(tZ._caScrollDist||0))}},eC.enable=function(e,r){eC.enabled||(eC.enabled=!0,ea(ed,"resize",ew),ey||ea(ed,"scroll",eb),ek&&ea(t,"refreshInit",ek),!1!==e&&(eC.progress=ej=0,l=u=eO=eL()),!1!==r&&eC.refresh())},eC.getTween=function(t){return t&&o?o.tween:q},eC.setPositions=function(t,e,r,n){if(tZ){var i=tZ.scrollTrigger,o=tZ.duration(),s=i.end-i.start;t=i.start+s*t/o,e=i.start+s*e/o}eC.refresh(!1,!1,{start:tM(t,r&&!!eC._startClamp),end:tM(e,r&&!!eC._endClamp)},n),eC.update()},eC.adjustPinSpacing=function(t){if(I&&t){var e=I.indexOf(es.d)+1;I[e]=parseFloat(I[e])+t+"px",I[1]=parseFloat(I[1])+t+"px",eW(I)}},eC.disable=function(e,r){if(eC.enabled&&(!1!==e&&eC.revert(!0,!0),eC.enabled=eC.isActive=!1,r||q&&q.pause(),ti=0,s&&(s.uncache=1),ek&&el(t,"refreshInit",ek),tn&&(tn.pause(),o.tween&&o.tween.kill()&&(o.tween=0)),!ey)){for(var n=eg.length;n--;)if(eg[n].scroller===ed&&eg[n]!==eC)return;el(ed,"resize",ew),ey||el(ed,"scroll",eb)}},eC.kill=function(t,n){eC.disable(t,n),q&&!n&&q.kill(),tf&&delete ev[tf];var i=eg.indexOf(eC);i>=0&&eg.splice(i,1),i===tr&&eI>0&&tr--,i=0,eg.forEach(function(t){return t.scroller===eC.scroller&&(i=1)}),i||tT||(eC.scroll.rec=0),r&&(r.scrollTrigger=null,t&&r.revert({kill:!1}),n||r.kill()),d&&[d,f,p,m].forEach(function(t){return t.parentNode&&t.parentNode.removeChild(t)}),tE===eC&&(tE=0),ty&&(s&&(s.uncache=1),i=0,eg.forEach(function(t){return t.pin===ty&&i++}),i||(s.spacer=0)),e.onKill&&e.onKill(eC)},eg.push(eC),eC.enable(!1,!1),tu&&tu(eC),r&&r.add&&!x){var ez=eC.update;eC.update=function(){eC.update=ez,y.cache++,c||h||eC.refresh()},U.delayedCall(.01,eC.update),x=.01,c=h=0}else eC.refresh();ty&&eM()},t.register=function(e){return z||(U=e||tV(),tI()&&window.document&&t.enable(),z=tR),z},t.defaults=function(t){if(t)for(var e in t)eh[e]=t[e];return eh},t.disable=function(t,e){tR=0,eg.forEach(function(r){return r[e?"kill":"disable"](t)}),el(W,"wheel",eb),el(H,"scroll",eb),clearInterval(J),el(H,"touchcancel",tj),el(X,"touchstart",tj),es(el,H,"pointerdown,touchstart,mousedown",tO),es(el,H,"pointerup,touchend,mouseup",tD),q.kill(),tW(el);for(var r=0;r<y.length;r+=3)eu(el,y[r],y[r+1]),eu(el,y[r],y[r+2])},t.enable=function(){if(W=window,Y=(H=document).documentElement,X=H.body,U&&(G=U.utils.toArray,K=U.utils.clamp,tp=U.core.context||tj,ta=U.core.suppressOverwrites||tj,tm=W.history.scrollRestoration||"auto",eL=W.pageYOffset||0,U.core.globals("ScrollTrigger",t),X)){tR=1,(tg=document.createElement("div")).style.height="100vh",tg.style.position="absolute",eO(),function t(){return tR&&requestAnimationFrame(t)}(),N.register(U),t.isTouch=N.isTouch,tf=N.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),tc=1===N.isTouch,ea(W,"wheel",eb),$=[W,H,Y,X],U.matchMedia?(t.matchMedia=function(t){var e,r=U.matchMedia();for(e in t)r.add(e,t[e]);return r},U.addEventListener("matchMediaInit",function(){return ek()}),U.addEventListener("matchMediaRevert",function(){return eC()}),U.addEventListener("matchMedia",function(){ej(0,1),eA("matchMedia")}),U.matchMedia().add("(orientation: portrait)",function(){return ex(),ex})):console.warn("Requires GSAP 3.11.0 or later"),ex(),ea(H,"scroll",eb);var e,r,n=X.hasAttribute("style"),i=X.style,o=i.borderTopStyle,s=U.core.Animation.prototype;for(s.revert||Object.defineProperty(s,"revert",{value:function(){return this.time(-.01,!0)}}),i.borderTopStyle="solid",e=er(X),O.m=Math.round(e.top+O.sc())||0,M.m=Math.round(e.left+M.sc())||0,o?i.borderTopStyle=o:i.removeProperty("border-top-style"),n||(X.setAttribute("style",""),X.removeAttribute("style")),J=setInterval(ey,250),U.delayedCall(.5,function(){return tA=0}),ea(H,"touchcancel",tj),ea(X,"touchstart",tj),es(ea,H,"pointerdown,touchstart,mousedown",tO),es(ea,H,"pointerup,touchend,mouseup",tD),te=U.utils.checkPrefix("transform"),eF.push(te),z=tP(),q=U.delayedCall(.2,ej).pause(),to=[H,"visibilitychange",function(){var t=W.innerWidth,e=W.innerHeight;H.hidden?(tn=t,ti=e):(tn!==t||ti!==e)&&ew()},H,"DOMContentLoaded",ej,W,"load",ej,W,"resize",ew],tW(ea),eg.forEach(function(t){return t.enable(0,1)}),r=0;r<y.length;r+=3)eu(el,y[r],y[r+1]),eu(el,y[r],y[r+2])}},t.config=function(e){"limitCallbacks"in e&&(tx=!!e.limitCallbacks);var r=e.syncInterval;r&&clearInterval(J)||(J=r)&&setInterval(ey,r),"ignoreMobileResize"in e&&(tc=1===t.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(tW(el)||tW(ea,e.autoRefreshEvents||"none"),tl=-1===(e.autoRefreshEvents+"").indexOf("resize"))},t.scrollerProxy=function(t,e){var r=D(t),n=y.indexOf(r),i=tB(r);~n&&y.splice(n,i?6:2),e&&(i?b.unshift(W,e,X,e,Y,e):b.unshift(r,e))},t.clearMatchMedia=function(t){eg.forEach(function(e){return e._ctx&&e._ctx.query===t&&e._ctx.kill(!0,!0)})},t.isInViewport=function(t,e,r){var n=(tH(t)?D(t):t).getBoundingClientRect(),i=n[r?t0:t1]*e||0;return r?n.right-i>0&&n.left+i<W.innerWidth:n.bottom-i>0&&n.top+i<W.innerHeight},t.positionInViewport=function(t,e,r){tH(t)&&(t=D(t));var n=t.getBoundingClientRect(),i=n[r?t0:t1],o=null==e?i/2:e in ed?ed[e]*i:~e.indexOf("%")?parseFloat(e)*i/100:parseFloat(e)||0;return r?(n.left+o)/W.innerWidth:(n.top+o)/W.innerHeight},t.killAll=function(t){if(eg.slice(0).forEach(function(t){return"ScrollSmoother"!==t.vars.id&&t.kill()}),!0!==t){var e=eT.killAll||[];eT={},e.forEach(function(t){return t()})}},t}()).version="3.12.7",eQ.saveStyles=function(t){return t?G(t).forEach(function(t){if(t&&t.style){var e=eP.indexOf(t);e>=0&&eP.splice(e,5),eP.push(t,t.style.cssText,t.getBBox&&t.getAttribute("transform"),U.core.getCache(t),tp())}}):eP},eQ.revert=function(t,e){return ek(!t,e)},eQ.create=function(t,e){return new eQ(t,e)},eQ.refresh=function(t){return t?ew(!0):(z||eQ.register())&&ej(!0)},eQ.update=function(t){return++y.cache&&eV(!0===t?2:0)},eQ.clearScrollMemory=eR,eQ.maxScroll=function(t,e){return tz(t,e?M:O)},eQ.getScrollFunc=function(t,e){return j(D(t),e?M:O)},eQ.getById=function(t){return ev[t]},eQ.getAll=function(){return eg.filter(function(t){return"ScrollSmoother"!==t.vars.id})},eQ.isScrolling=function(){return!!tk},eQ.snapDirectional=eo,eQ.addEventListener=function(t,e){var r=eT[t]||(eT[t]=[]);~r.indexOf(e)||r.push(e)},eQ.removeEventListener=function(t,e){var r=eT[t],n=r&&r.indexOf(e);n>=0&&r.splice(n,1)},eQ.batch=function(t,e){var r,n=[],i={},o=e.interval||.016,s=e.batchMax||1e9,a=function(t,e){var r=[],n=[],i=U.delayedCall(o,function(){e(r,n),r=[],n=[]}).pause();return function(t){r.length||i.restart(!0),r.push(t.trigger),n.push(t),s<=r.length&&i.progress(1)}};for(r in e)i[r]="on"===r.substr(0,2)&&tY(e[r])&&"onRefreshInit"!==r?a(r,e[r]):e[r];return tY(s)&&(s=s(),ea(eQ,"refresh",function(){return s=e.batchMax()})),G(t).forEach(function(t){var e={};for(r in i)e[r]=i[r];e.trigger=t,n.push(eQ.create(e))}),n},e1=function(t,e,r,n){return e>n?t(n):e<0&&t(0),r>n?(n-e)/(r-e):r<0?e/(e-r):1},e2=function t(e,r){!0===r?e.style.removeProperty("touch-action"):e.style.touchAction=!0===r?"auto":r?"pan-"+r+(N.isTouch?" pinch-zoom":""):"none",e===Y&&t(X,r)},e5={auto:1,scroll:1},e3=function(t){var e,r=t.event,n=t.target,i=t.axis,o=(r.changedTouches?r.changedTouches[0]:r).target,s=o._gsap||U.core.getCache(o),a=tP();if(!s._isScrollT||a-s._isScrollT>2e3){for(;o&&o!==X&&(o.scrollHeight<=o.clientHeight&&o.scrollWidth<=o.clientWidth||!(e5[(e=t7(o)).overflowY]||e5[e.overflowX]));)o=o.parentNode;s._isScroll=o&&o!==n&&!tB(o)&&(e5[(e=t7(o)).overflowY]||e5[e.overflowX]),s._isScrollT=a}(s._isScroll||"x"===i)&&(r.stopPropagation(),r._gsapAllow=!0)},e6=function(t,e,r,n){return N.create({target:t,capture:!0,debounce:!1,lockAxis:!0,type:e,onWheel:n=n&&e3,onPress:n,onDrag:n,onScroll:n,onEnable:function(){return r&&ea(H,N.eventTypes[0],e4,!1,!0)},onDisable:function(){return el(H,N.eventTypes[0],e4,!0)}})},e8=/(input|label|select|textarea)/i,e4=function(t){var e=e8.test(t.target.tagName);(e||e0)&&(t._gsapAllow=!0,e0=e)},e9=function(t){t$(t)||(t={}),t.preventDefault=t.isNormalizer=t.allowClicks=!0,t.type||(t.type="wheel,touch"),t.debounce=!!t.debounce,t.id=t.id||"normalizer";var e,r,n,i,o,s,a,l,u=t,c=u.normalizeScrollX,h=u.momentum,d=u.allowNestedScroll,f=u.onRelease,p=D(t.target)||Y,m=U.core.globals().ScrollSmoother,g=m&&m.get(),v=tf&&(t.content&&D(t.content)||g&&!1!==t.content&&!g.smooth()&&g.content()),b=j(p,O),x=j(p,M),w=1,T=(N.isTouch&&W.visualViewport?W.visualViewport.scale*W.visualViewport.width:W.outerWidth)/W.innerWidth,S=0,E=tY(h)?function(){return h(e)}:function(){return h||2.8},A=e6(p,t.type,!0,d),P=function(){return i=!1},C=tj,k=tj,R=function(){r=tz(p,O),k=K(tf?1:0,r),c&&(C=K(0,tz(p,M))),n=e_},_=function(){v._gsap.y=tL(parseFloat(v._gsap.y)+b.offset)+"px",v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(v._gsap.y)+", 0, 1)",b.offset=b.cacheID=0},L=function(){if(i){requestAnimationFrame(P);var t=tL(e.deltaY/2),r=k(b.v-t);if(v&&r!==b.v+b.offset){b.offset=r-b.v;var n=tL((parseFloat(v&&v._gsap.y)||0)-b.offset);v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+n+", 0, 1)",v._gsap.y=n+"px",b.cacheID=y.cache,eV()}return!0}b.offset&&_(),i=!0},I=function(){R(),o.isActive()&&o.vars.scrollY>r&&(b()>r?o.progress(1)&&b(r):o.resetTo("scrollY",r))};return v&&U.set(v,{y:"+=0"}),t.ignoreCheck=function(t){return tf&&"touchmove"===t.type&&L()||w>1.05&&"touchstart"!==t.type||e.isGesturing||t.touches&&t.touches.length>1},t.onPress=function(){i=!1;var t=w;w=tL((W.visualViewport&&W.visualViewport.scale||1)/T),o.pause(),t!==w&&e2(p,w>1.01||!c&&"x"),s=x(),a=b(),R(),n=e_},t.onRelease=t.onGestureStart=function(t,e){if(b.offset&&_(),e){y.cache++;var n,i,s=E();c&&(i=(n=x())+-(.05*s*t.velocityX)/.227,s*=e1(x,n,i,tz(p,M)),o.vars.scrollX=C(i)),i=(n=b())+-(.05*s*t.velocityY)/.227,s*=e1(b,n,i,tz(p,O)),o.vars.scrollY=k(i),o.invalidate().duration(s).play(.01),(tf&&o.vars.scrollY>=r||n>=r-1)&&U.to({},{onUpdate:I,duration:s})}else l.restart(!0);f&&f(t)},t.onWheel=function(){o._ts&&o.pause(),tP()-S>1e3&&(n=0,S=tP())},t.onChange=function(t,e,r,i,o){if(e_!==n&&R(),e&&c&&x(C(i[2]===e?s+(t.startX-t.x):x()+e-i[1])),r){b.offset&&_();var l=o[2]===r,u=l?a+t.startY-t.y:b()+r-o[1],h=k(u);l&&u!==h&&(a+=h-u),b(h)}(r||e)&&eV()},t.onEnable=function(){e2(p,!c&&"x"),eQ.addEventListener("refresh",I),ea(W,"resize",I),b.smooth&&(b.target.style.scrollBehavior="auto",b.smooth=x.smooth=!1),A.enable()},t.onDisable=function(){e2(p,!0),el(W,"resize",I),eQ.removeEventListener("refresh",I),A.kill()},t.lockAxis=!1!==t.lockAxis,(e=new N(t)).iOS=tf,tf&&!b()&&b(1),tf&&U.ticker.add(tj),l=e._dc,o=U.to(e,{ease:"power4",paused:!0,inherit:!1,scrollX:c?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:eK(b,b(),function(){return o.pause()})},onUpdate:eV,onComplete:l.vars.onComplete}),e},eQ.sort=function(t){if(tY(t))return eg.sort(t);var e=W.pageYOffset||0;return eQ.getAll().forEach(function(t){return t._sortY=t.trigger?e+t.trigger.getBoundingClientRect().top:t.start+W.innerHeight}),eg.sort(t||function(t,e){return -1e6*(t.vars.refreshPriority||0)+(t.vars.containerAnimation?1e6:t._sortY)-((e.vars.containerAnimation?1e6:e._sortY)+-1e6*(e.vars.refreshPriority||0))})},eQ.observe=function(t){return new N(t)},eQ.normalizeScroll=function(t){if(void 0===t)return tu;if(!0===t&&tu)return tu.enable();if(!1===t){tu&&tu.kill(),tu=t;return}var e=t instanceof N?t:e9(t);return tu&&tu.target===e.target&&tu.kill(),tB(e.target)&&(tu=e),e},eQ.core={_getVelocityProp:L,_inputObserver:e6,_scrollers:y,_proxies:b,bridge:{ss:function(){tk||eA("scrollStart"),tk=tP()},ref:function(){return Q}}},tV()&&U.registerPlugin(eQ),e.ScrollTrigger=eQ,e.default=eQ,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete window.default},1694:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CARRIAGE_RETURN_PLACEHOLDER_REGEX=e.CARRIAGE_RETURN_PLACEHOLDER=e.CARRIAGE_RETURN_REGEX=e.CARRIAGE_RETURN=e.CASE_SENSITIVE_TAG_NAMES_MAP=e.CASE_SENSITIVE_TAG_NAMES=void 0,e.CASE_SENSITIVE_TAG_NAMES=["animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","linearGradient","radialGradient","textPath"],e.CASE_SENSITIVE_TAG_NAMES_MAP=e.CASE_SENSITIVE_TAG_NAMES.reduce(function(t,e){return t[e.toLowerCase()]=e,t},{}),e.CARRIAGE_RETURN="\r",e.CARRIAGE_RETURN_REGEX=RegExp(e.CARRIAGE_RETURN,"g"),e.CARRIAGE_RETURN_PLACEHOLDER="__HTML_DOM_PARSER_CARRIAGE_RETURN_PLACEHOLDER_".concat(Date.now(),"__"),e.CARRIAGE_RETURN_PLACEHOLDER_REGEX=RegExp(e.CARRIAGE_RETURN_PLACEHOLDER,"g")},5305:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e,r,f=(t=(0,i.escapeSpecialCharacters)(t)).match(l),p=f&&f[1]?f[1].toLowerCase():"";switch(p){case o:var m=d(t);if(!u.test(t)){var g=m.querySelector(s);null===(e=null==g?void 0:g.parentNode)||void 0===e||e.removeChild(g)}if(!c.test(t)){var g=m.querySelector(a);null===(r=null==g?void 0:g.parentNode)||void 0===r||r.removeChild(g)}return m.querySelectorAll(o);case s:case a:var v=h(t).querySelectorAll(p);if(c.test(t)&&u.test(t))return v[0].parentNode.childNodes;return v;default:if(n)return n(t);var g=h(t,a).querySelector(a);return g.childNodes}};var n,i=r(6082),o="html",s="head",a="body",l=/<([a-zA-Z]+[0-9]?)/,u=/<head[^]*>/i,c=/<body[^]*>/i,h=function(t,e){throw Error("This browser does not support `document.implementation.createHTMLDocument`")},d=function(t,e){throw Error("This browser does not support `DOMParser.prototype.parseFromString`")},f="object"==typeof window&&window.DOMParser;if("function"==typeof f){var p=new f;h=d=function(t,e){return e&&(t="<".concat(e,">").concat(t,"</").concat(e,">")),p.parseFromString(t,"text/html")}}if("object"==typeof document&&document.implementation){var m=document.implementation.createHTMLDocument();h=function(t,e){if(e){var r=m.documentElement.querySelector(e);return r&&(r.innerHTML=t),m}return m.documentElement.innerHTML=t,m}}var g="object"==typeof document&&document.createElement("template");g&&g.content&&(n=function(t){return g.innerHTML=t,g.content.childNodes})},7612:function(t,e,r){"use strict";var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("string"!=typeof t)throw TypeError("First argument must be a string");if(!t)return[];var e=t.match(s),r=e?e[1]:void 0;return(0,o.formatDOM)((0,i.default)(t),null,r)};var i=n(r(5305)),o=r(6082),s=/<(![a-zA-Z\s]+)>/},6082:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.formatAttributes=o,e.escapeSpecialCharacters=function(t){return t.replace(i.CARRIAGE_RETURN_REGEX,i.CARRIAGE_RETURN_PLACEHOLDER)},e.revertEscapedCharacters=s,e.formatDOM=function t(e,r,a){void 0===r&&(r=null);for(var l,u=[],c=0,h=e.length;c<h;c++){var d=e[c];switch(d.nodeType){case 1:var f=function(t){var e;return e=t=t.toLowerCase(),i.CASE_SENSITIVE_TAG_NAMES_MAP[e]||t}(d.nodeName);(l=new n.Element(f,o(d.attributes))).children=t("template"===f?d.content.childNodes:d.childNodes,l);break;case 3:l=new n.Text(s(d.nodeValue));break;case 8:l=new n.Comment(d.nodeValue);break;default:continue}var p=u[c-1]||null;p&&(p.next=l),l.parent=r,l.prev=p,l.next=null,u.push(l)}return a&&((l=new n.ProcessingInstruction(a.substring(0,a.indexOf(" ")).toLowerCase(),a)).next=u[0]||null,l.parent=r,u.unshift(l),u[1]&&(u[1].prev=u[0])),u};var n=r(8131),i=r(1694);function o(t){for(var e={},r=0,n=t.length;r<n;r++){var i=t[r];e[i.name]=i.value}return e}function s(t){return t.replace(i.CARRIAGE_RETURN_PLACEHOLDER_REGEX,i.CARRIAGE_RETURN)}},4509:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){void 0===t&&(t={});var r={},u=!!(t.type&&a[t.type]);for(var c in t){var h=t[c];if((0,n.isCustomAttribute)(c)){r[c]=h;continue}var d=c.toLowerCase(),f=l(d);if(f){var p=(0,n.getPropertyInfo)(f);switch(o.includes(f)&&s.includes(e)&&!u&&(f=l("default"+d)),r[f]=h,p&&p.type){case n.BOOLEAN:r[f]=!0;break;case n.OVERLOADED_BOOLEAN:""===h&&(r[f]=!0)}continue}i.PRESERVE_CUSTOM_ATTRIBUTES&&(r[c]=h)}return(0,i.setStyleProp)(t.style,r),r};var n=r(2803),i=r(690),o=["checked","value"],s=["input","select","textarea"],a={reset:!0,submit:!0};function l(t){return n.possibleStandardNames[t]}},2523:function(t,e,r){"use strict";var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.default=function t(e,r){void 0===r&&(r={});for(var n=[],i="function"==typeof r.replace,l=r.transform||s.returnFirstArg,u=r.library||a,c=u.cloneElement,h=u.createElement,d=u.isValidElement,f=e.length,p=0;p<f;p++){var m=e[p];if(i){var g=r.replace(m,p);if(d(g)){f>1&&(g=c(g,{key:g.key||p})),n.push(l(g,m,p));continue}}if("text"===m.type){var v=!m.data.trim().length;if(v&&m.parent&&!(0,s.canTextBeChildOfNode)(m.parent)||r.trim&&v)continue;n.push(l(m.data,m,p));continue}var y={};s.PRESERVE_CUSTOM_ATTRIBUTES&&"tag"===m.type&&(0,s.isCustomComponent)(m.name,m.attribs)?((0,s.setStyleProp)(m.attribs.style,m.attribs),y=m.attribs):m.attribs&&(y=(0,o.default)(m.attribs,m.name));var b=void 0;switch(m.type){case"script":case"style":m.children[0]&&(y.dangerouslySetInnerHTML={__html:m.children[0].data});break;case"tag":"textarea"===m.name&&m.children[0]?y.defaultValue=m.children[0].data:m.children&&m.children.length&&(b=t(m.children,r));break;default:continue}f>1&&(y.key=p),n.push(l(h(m.name,y,b),m,p))}return 1===n.length?n[0]:n};var i=r(2265),o=n(r(4509)),s=r(690),a={cloneElement:i.cloneElement,createElement:i.createElement,isValidElement:i.isValidElement}},753:function(t,e,r){"use strict";var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.htmlToDOM=e.domToReact=e.attributesToProps=e.Text=e.ProcessingInstruction=e.Element=e.Comment=void 0,e.default=function(t,e){if("string"!=typeof t)throw TypeError("First argument must be a string");return t?(0,s.default)((0,i.default)(t,(null==e?void 0:e.htmlparser2)||l),e):[]};var i=n(r(7612));e.htmlToDOM=i.default;var o=n(r(4509));e.attributesToProps=o.default;var s=n(r(2523));e.domToReact=s.default;var a=r(8131);Object.defineProperty(e,"Comment",{enumerable:!0,get:function(){return a.Comment}}),Object.defineProperty(e,"Element",{enumerable:!0,get:function(){return a.Element}}),Object.defineProperty(e,"ProcessingInstruction",{enumerable:!0,get:function(){return a.ProcessingInstruction}}),Object.defineProperty(e,"Text",{enumerable:!0,get:function(){return a.Text}});var l={lowerCaseAttributeNames:!1}},690:function(t,e,r){"use strict";var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.returnFirstArg=e.canTextBeChildOfNode=e.ELEMENTS_WITH_NO_TEXT_CHILDREN=e.PRESERVE_CUSTOM_ATTRIBUTES=void 0,e.isCustomComponent=function(t,e){return t.includes("-")?!s.has(t):!!(e&&"string"==typeof e.is)},e.setStyleProp=function(t,e){if("string"==typeof t){if(!t.trim()){e.style={};return}try{e.style=(0,o.default)(t,a)}catch(t){e.style={}}}};var i=r(2265),o=n(r(3773)),s=new Set(["annotation-xml","color-profile","font-face","font-face-src","font-face-uri","font-face-format","font-face-name","missing-glyph"]),a={reactCompat:!0};e.PRESERVE_CUSTOM_ATTRIBUTES=Number(i.version.split(".")[0])>=16,e.ELEMENTS_WITH_NO_TEXT_CHILDREN=new Set(["tr","tbody","thead","tfoot","colgroup","table","head","html","frameset"]),e.canTextBeChildOfNode=function(t){return!e.ELEMENTS_WITH_NO_TEXT_CHILDREN.has(t.name)},e.returnFirstArg=function(t){return t}},4958:function(t){var e=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,r=/\n/g,n=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,s=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,l=/^\s+|\s+$/g;function u(t){return t?t.replace(l,""):""}t.exports=function(t,l){if("string"!=typeof t)throw TypeError("First argument must be a string");if(!t)return[];l=l||{};var c=1,h=1;function d(t){var e=t.match(r);e&&(c+=e.length);var n=t.lastIndexOf("\n");h=~n?t.length-n:h+t.length}function f(){var t={line:c,column:h};return function(e){return e.position=new p(t),v(n),e}}function p(t){this.start=t,this.end={line:c,column:h},this.source=l.source}p.prototype.content=t;var m=[];function g(e){var r=Error(l.source+":"+c+":"+h+": "+e);if(r.reason=e,r.filename=l.source,r.line=c,r.column=h,r.source=t,l.silent)m.push(r);else throw r}function v(e){var r=e.exec(t);if(r){var n=r[0];return d(n),t=t.slice(n.length),r}}function y(t){var e;for(t=t||[];e=b();)!1!==e&&t.push(e);return t}function b(){var e=f();if("/"==t.charAt(0)&&"*"==t.charAt(1)){for(var r=2;""!=t.charAt(r)&&("*"!=t.charAt(r)||"/"!=t.charAt(r+1));)++r;if(r+=2,""===t.charAt(r-1))return g("End of comment missing");var n=t.slice(2,r-2);return h+=2,d(n),t=t.slice(r),h+=2,e({type:"comment",comment:n})}}return v(n),function(){var t,r=[];for(y(r);t=function(){var t=f(),r=v(i);if(r){if(b(),!v(o))return g("property missing ':'");var n=v(s),l=t({type:"declaration",property:u(r[0].replace(e,"")),value:n?u(n[0].replace(e,"")):""});return v(a),l}}();)!1!==t&&(r.push(t),y(r));return r}()}},357:function(t,e,r){"use strict";var n,i;t.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(i=r.g.process)?void 0:i.env)?r.g.process:r(8081)},6300:function(t){!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=l(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,o=l(t),s=o[0],a=o[1],u=new i((s+a)*3/4-a),c=0,h=a>0?s-4:s;for(r=0;r<h;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],u[c++]=e>>16&255,u[c++]=e>>8&255,u[c++]=255&e;return 2===a&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,u[c++]=255&e),1===a&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,u[c++]=e>>8&255,u[c++]=255&e),u},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],s=0,a=n-i;s<a;s+=16383)o.push(function(t,e,n){for(var i,o=[],s=e;s<n;s+=3)o.push(r[(i=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]))>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(t,s,s+16383>a?a:s+16383));return 1===i?o.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&o.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=o.length;s<a;++s)r[s]=o[s],n[o.charCodeAt(s)]=s;function l(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},72:function(t,e,r){"use strict";/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var n=r(675),i=r(783),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(t){if(t>**********)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return c(t)}return l(t,e,r)}function l(t,e,r){if("string"==typeof t)return function(t,e){if(("string"!=typeof e||""===e)&&(e="utf8"),!a.isEncoding(e))throw TypeError("Unknown encoding: "+e);var r=0|f(t,e),n=s(r),i=n.write(t,e);return i!==r&&(n=n.slice(0,i)),n}(t,e);if(ArrayBuffer.isView(t))return h(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(R(t,ArrayBuffer)||t&&R(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(R(t,SharedArrayBuffer)||t&&R(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return a.from(n,e,r);var i=function(t){if(a.isBuffer(t)){var e,r=0|d(t.length),n=s(r);return 0===n.length||t.copy(n,0,0,r),n}return void 0!==t.length?"number"!=typeof t.length||(e=t.length)!=e?s(0):h(t):"Buffer"===t.type&&Array.isArray(t.data)?h(t.data):void 0}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function u(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function c(t){return u(t),s(t<0?0:0|d(t))}function h(t){for(var e=t.length<0?0:0|d(t.length),r=s(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function d(t){if(t>=**********)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function f(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||R(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return A(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return C(t).length;default:if(i)return n?-1:A(t).length;e=(""+e).toLowerCase(),i=!0}}function p(t,e,r){var i,o,s=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=_[t[o]];return i}(this,e,r);case"utf8":case"utf-8":return y(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=e,o=r,0===i&&o===this.length?n.fromByteArray(this):n.fromByteArray(this.slice(i,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,e,r);default:if(s)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),s=!0}}function m(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function g(t,e,r,n,i){var o;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),(o=r=+r)!=o&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return -1;r=t.length-1}else if(r<0){if(!i)return -1;r=0}if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:v(t,e,r,n,i);if("number"==typeof e)return(e&=255,"function"==typeof Uint8Array.prototype.indexOf)?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):v(t,[e],r,n,i);throw TypeError("val must be string, number or Buffer")}function v(t,e,r,n,i){var o,s=1,a=t.length,l=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;s=2,a/=2,l/=2,r/=2}function u(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var c=-1;for(o=r;o<a;o++)if(u(t,o)===u(e,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===l)return c*s}else -1!==c&&(o-=o-c),c=-1}else for(r+l>a&&(r=a-l),o=r;o>=0;o--){for(var h=!0,d=0;d<l;d++)if(u(t,o+d)!==u(e,d)){h=!1;break}if(h)return o}return -1}function y(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,a,l,u=t[i],c=null,h=u>239?4:u>223?3:u>191?2:1;if(i+h<=r)switch(h){case 1:u<128&&(c=u);break;case 2:(192&(o=t[i+1]))==128&&(l=(31&u)<<6|63&o)>127&&(c=l);break;case 3:o=t[i+1],s=t[i+2],(192&o)==128&&(192&s)==128&&(l=(15&u)<<12|(63&o)<<6|63&s)>2047&&(l<55296||l>57343)&&(c=l);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],(192&o)==128&&(192&s)==128&&(192&a)==128&&(l=(15&u)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&l<1114112&&(c=l)}null===c?(c=65533,h=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=h}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);for(var r="",n=0;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=4096));return r}(n)}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function x(t,e,r,n,i,o){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function w(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function T(t,e,r,n,o){return e=+e,r>>>=0,o||w(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function S(t,e,r,n,o){return e=+e,r>>>=0,o||w(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}e.Buffer=a,e.SlowBuffer=function(t){return+t!=t&&(t=0),a.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=**********,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return l(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(u(t),t<=0)?s(t):void 0!==e?"string"==typeof r?s(t).fill(e,r):s(t).fill(e):s(t)},a.allocUnsafe=function(t){return c(t)},a.allocUnsafeSlow=function(t){return c(t)},a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(R(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),R(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(R(o,Uint8Array)&&(o=a.from(o)),!a.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},a.byteLength=f,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)m(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)m(this,e,e+3),m(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)m(this,e,e+7),m(this,e+1,e+6),m(this,e+2,e+5),m(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?y(this,0,t):p.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(a.prototype[o]=a.prototype.inspect),a.prototype.compare=function(t,e,r,n,i){if(R(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var o=i-n,s=r-e,l=Math.min(o,s),u=this.slice(n,i),c=t.slice(e,r),h=0;h<l;++h)if(u[h]!==c[h]){o=u[h],s=c[h];break}return o<s?-1:s<o?1:0},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return g(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return g(this,t,e,r,!1)},a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,s,a,l,u,c,h,d,f,p,m,g=this.length-e;if((void 0===r||r>g)&&(r=g),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var v=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(e.substr(2*s,2),16);if(a!=a)break;t[r+s]=a}return s}(this,t,e,r);case"utf8":case"utf-8":return l=e,u=r,k(A(t,this.length-l),this,l,u);case"ascii":return c=e,h=r,k(P(t),this,c,h);case"latin1":case"binary":return i=this,o=t,s=e,a=r,k(P(o),i,s,a);case"base64":return d=e,f=r,k(C(t),this,d,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return p=e,m=r,k(function(t,e){for(var r,n,i=[],o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-p),this,p,m);default:if(v)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),v=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},a.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var o=1,s=0;for(this[e]=255&t;++s<r&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}var o=r-1,s=1;for(this[e+o]=255&t;--o>=0&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,**********,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||x(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return T(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return T(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return S(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return S(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var o=i-1;o>=0;--o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var s=a.isBuffer(t)?t:a.from(t,n),l=s.length;if(0===l)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=s[i%l]}return this};var E=/[^+/0-9A-Za-z-_]/g;function A(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319||s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function P(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function C(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(E,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function k(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function R(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var _=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},783:function(t,e){/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,l=(1<<a)-1,u=l>>1,c=-7,h=r?i-1:0,d=r?-1:1,f=t[e+h];for(h+=d,o=f&(1<<-c)-1,f>>=-c,c+=a;c>0;o=256*o+t[e+h],h+=d,c-=8);for(s=o&(1<<-c)-1,o>>=-c,c+=n;c>0;s=256*s+t[e+h],h+=d,c-=8);if(0===o)o=1-u;else{if(o===l)return s?NaN:1/0*(f?-1:1);s+=Math.pow(2,n),o-=u}return(f?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,l,u=8*o-i-1,c=(1<<u)-1,h=c>>1,d=23===i?5960464477539062e-23:0,f=n?0:o-1,p=n?1:-1,m=e<0||0===e&&1/e<0?1:0;for(isNaN(e=Math.abs(e))||e===1/0?(a=isNaN(e)?1:0,s=c):(s=Math.floor(Math.log(e)/Math.LN2),e*(l=Math.pow(2,-s))<1&&(s--,l*=2),s+h>=1?e+=d/l:e+=d*Math.pow(2,1-h),e*l>=2&&(s++,l/=2),s+h>=c?(a=0,s=c):s+h>=1?(a=(e*l-1)*Math.pow(2,i),s+=h):(a=e*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;t[r+f]=255&a,f+=p,a/=256,i-=8);for(s=s<<i|a,u+=i;u>0;t[r+f]=255&s,f+=p,s/=256,u-=8);t[r+f-p]|=128*m}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var o=r[t]={exports:{}},s=!0;try{e[t](o,o.exports,n),s=!1}finally{s&&delete r[t]}return o.exports}n.ab="//";var i=n(72);t.exports=i}()},6810:function(){},8081:function(t){!function(){var e={229:function(t){var e,r,n,i=t.exports={};function o(){throw Error("setTimeout has not been defined")}function s(){throw Error("clearTimeout has not been defined")}function a(t){if(e===setTimeout)return setTimeout(t,0);if((e===o||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(r){try{return e.call(null,t,0)}catch(r){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:o}catch(t){e=o}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(t){r=s}}();var l=[],u=!1,c=-1;function h(){u&&n&&(u=!1,n.length?l=n.concat(l):c=-1,l.length&&d())}function d(){if(!u){var t=a(h);u=!0;for(var e=l.length;e;){for(n=l,l=[];++c<e;)n&&n[c].run();c=-1,e=l.length}n=null,u=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(e){try{return r.call(null,t)}catch(e){return r.call(this,t)}}}(t)}}function f(t,e){this.fun=t,this.array=e}function p(){}i.nextTick=function(t){var e=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];l.push(new f(t,e)),1!==l.length||u||a(d)},f.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=p,i.addListener=p,i.once=p,i.off=p,i.removeListener=p,i.removeAllListeners=p,i.emit=p,i.prependListener=p,i.prependOnceListener=p,i.listeners=function(t){return[]},i.binding=function(t){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var o=r[t]={exports:{}},s=!0;try{e[t](o,o.exports,n),s=!1}finally{s&&delete r[t]}return o.exports}n.ab="//";var i=n(229);t.exports=i}()},912:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"BailoutToCSR",{enumerable:!0,get:function(){return i}});let n=r(5592);function i(t){let{reason:e,children:r}=t;if("undefined"==typeof window)throw new n.BailoutToCSRError(e);return r}},1481:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"PreloadCss",{enumerable:!0,get:function(){return o}});let n=r(7437),i=r(8512);function o(t){let{moduleIds:e}=t;if("undefined"!=typeof window)return null;let r=(0,i.getExpectedRequestStore)("next/dynamic css"),o=[];if(r.reactLoadableManifest&&e){let t=r.reactLoadableManifest;for(let r of e){if(!t[r])continue;let e=t[r].files.filter(t=>t.endsWith(".css"));o.push(...e)}}return 0===o.length?null:(0,n.jsx)(n.Fragment,{children:o.map(t=>(0,n.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(t),as:"style"},t))})}},9949:function(t,e,r){"use strict";var n=r(8877);function i(){}function o(){}o.resetWarningCache=i,t.exports=function(){function t(t,e,r,i,o,s){if(s!==n){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},1448:function(t,e,r){t.exports=r(9949)()},8877:function(t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},2803:function(t,e,r){"use strict";function n(t,e,r,n,i,o,s){this.acceptsBooleans=2===e||3===e||4===e,this.attributeName=n,this.attributeNamespace=i,this.mustUseProperty=r,this.propertyName=t,this.type=e,this.sanitizeURL=o,this.removeEmptyString=s}let i={};["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"].forEach(t=>{i[t]=new n(t,0,!1,t,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(([t,e])=>{i[t]=new n(t,1,!1,e,null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(t=>{i[t]=new n(t,2,!1,t.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(t=>{i[t]=new n(t,2,!1,t,null,!1,!1)}),["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","disableRemotePlayback","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach(t=>{i[t]=new n(t,3,!1,t.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(t=>{i[t]=new n(t,3,!0,t,null,!1,!1)}),["capture","download"].forEach(t=>{i[t]=new n(t,4,!1,t,null,!1,!1)}),["cols","rows","size","span"].forEach(t=>{i[t]=new n(t,6,!1,t,null,!1,!1)}),["rowSpan","start"].forEach(t=>{i[t]=new n(t,5,!1,t.toLowerCase(),null,!1,!1)});let o=/[\-\:]([a-z])/g,s=t=>t[1].toUpperCase();["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach(t=>{let e=t.replace(o,s);i[e]=new n(e,1,!1,t,null,!1,!1)}),["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach(t=>{let e=t.replace(o,s);i[e]=new n(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(t=>{let e=t.replace(o,s);i[e]=new n(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(t=>{i[t]=new n(t,1,!1,t.toLowerCase(),null,!1,!1)}),i.xlinkHref=new n("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(t=>{i[t]=new n(t,1,!1,t.toLowerCase(),null,!0,!0)});let{CAMELCASE:a,SAME:l,possibleStandardNames:u}=r(8596),c=RegExp.prototype.test.bind(RegExp("^(data|aria)-[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$")),h=Object.keys(u).reduce((t,e)=>{let r=u[e];return r===l?t[e]=e:r===a?t[e.toLowerCase()]=e:t[e]=r,t},{});e.BOOLEAN=3,e.BOOLEANISH_STRING=2,e.NUMERIC=5,e.OVERLOADED_BOOLEAN=4,e.POSITIVE_NUMERIC=6,e.RESERVED=0,e.STRING=1,e.getPropertyInfo=function(t){return i.hasOwnProperty(t)?i[t]:null},e.isCustomAttribute=c,e.possibleStandardNames=h},8596:function(t,e){e.SAME=0,e.CAMELCASE=1,e.possibleStandardNames={accept:0,acceptCharset:1,"accept-charset":"acceptCharset",accessKey:1,action:0,allowFullScreen:1,alt:0,as:0,async:0,autoCapitalize:1,autoComplete:1,autoCorrect:1,autoFocus:1,autoPlay:1,autoSave:1,capture:0,cellPadding:1,cellSpacing:1,challenge:0,charSet:1,checked:0,children:0,cite:0,class:"className",classID:1,className:1,cols:0,colSpan:1,content:0,contentEditable:1,contextMenu:1,controls:0,controlsList:1,coords:0,crossOrigin:1,dangerouslySetInnerHTML:1,data:0,dateTime:1,default:0,defaultChecked:1,defaultValue:1,defer:0,dir:0,disabled:0,disablePictureInPicture:1,disableRemotePlayback:1,download:0,draggable:0,encType:1,enterKeyHint:1,for:"htmlFor",form:0,formMethod:1,formAction:1,formEncType:1,formNoValidate:1,formTarget:1,frameBorder:1,headers:0,height:0,hidden:0,high:0,href:0,hrefLang:1,htmlFor:1,httpEquiv:1,"http-equiv":"httpEquiv",icon:0,id:0,innerHTML:1,inputMode:1,integrity:0,is:0,itemID:1,itemProp:1,itemRef:1,itemScope:1,itemType:1,keyParams:1,keyType:1,kind:0,label:0,lang:0,list:0,loop:0,low:0,manifest:0,marginWidth:1,marginHeight:1,max:0,maxLength:1,media:0,mediaGroup:1,method:0,min:0,minLength:1,multiple:0,muted:0,name:0,noModule:1,nonce:0,noValidate:1,open:0,optimum:0,pattern:0,placeholder:0,playsInline:1,poster:0,preload:0,profile:0,radioGroup:1,readOnly:1,referrerPolicy:1,rel:0,required:0,reversed:0,role:0,rows:0,rowSpan:1,sandbox:0,scope:0,scoped:0,scrolling:0,seamless:0,selected:0,shape:0,size:0,sizes:0,span:0,spellCheck:1,src:0,srcDoc:1,srcLang:1,srcSet:1,start:0,step:0,style:0,summary:0,tabIndex:1,target:0,title:0,type:0,useMap:1,value:0,width:0,wmode:0,wrap:0,about:0,accentHeight:1,"accent-height":"accentHeight",accumulate:0,additive:0,alignmentBaseline:1,"alignment-baseline":"alignmentBaseline",allowReorder:1,alphabetic:0,amplitude:0,arabicForm:1,"arabic-form":"arabicForm",ascent:0,attributeName:1,attributeType:1,autoReverse:1,azimuth:0,baseFrequency:1,baselineShift:1,"baseline-shift":"baselineShift",baseProfile:1,bbox:0,begin:0,bias:0,by:0,calcMode:1,capHeight:1,"cap-height":"capHeight",clip:0,clipPath:1,"clip-path":"clipPath",clipPathUnits:1,clipRule:1,"clip-rule":"clipRule",color:0,colorInterpolation:1,"color-interpolation":"colorInterpolation",colorInterpolationFilters:1,"color-interpolation-filters":"colorInterpolationFilters",colorProfile:1,"color-profile":"colorProfile",colorRendering:1,"color-rendering":"colorRendering",contentScriptType:1,contentStyleType:1,cursor:0,cx:0,cy:0,d:0,datatype:0,decelerate:0,descent:0,diffuseConstant:1,direction:0,display:0,divisor:0,dominantBaseline:1,"dominant-baseline":"dominantBaseline",dur:0,dx:0,dy:0,edgeMode:1,elevation:0,enableBackground:1,"enable-background":"enableBackground",end:0,exponent:0,externalResourcesRequired:1,fill:0,fillOpacity:1,"fill-opacity":"fillOpacity",fillRule:1,"fill-rule":"fillRule",filter:0,filterRes:1,filterUnits:1,floodOpacity:1,"flood-opacity":"floodOpacity",floodColor:1,"flood-color":"floodColor",focusable:0,fontFamily:1,"font-family":"fontFamily",fontSize:1,"font-size":"fontSize",fontSizeAdjust:1,"font-size-adjust":"fontSizeAdjust",fontStretch:1,"font-stretch":"fontStretch",fontStyle:1,"font-style":"fontStyle",fontVariant:1,"font-variant":"fontVariant",fontWeight:1,"font-weight":"fontWeight",format:0,from:0,fx:0,fy:0,g1:0,g2:0,glyphName:1,"glyph-name":"glyphName",glyphOrientationHorizontal:1,"glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphOrientationVertical:1,"glyph-orientation-vertical":"glyphOrientationVertical",glyphRef:1,gradientTransform:1,gradientUnits:1,hanging:0,horizAdvX:1,"horiz-adv-x":"horizAdvX",horizOriginX:1,"horiz-origin-x":"horizOriginX",ideographic:0,imageRendering:1,"image-rendering":"imageRendering",in2:0,in:0,inlist:0,intercept:0,k1:0,k2:0,k3:0,k4:0,k:0,kernelMatrix:1,kernelUnitLength:1,kerning:0,keyPoints:1,keySplines:1,keyTimes:1,lengthAdjust:1,letterSpacing:1,"letter-spacing":"letterSpacing",lightingColor:1,"lighting-color":"lightingColor",limitingConeAngle:1,local:0,markerEnd:1,"marker-end":"markerEnd",markerHeight:1,markerMid:1,"marker-mid":"markerMid",markerStart:1,"marker-start":"markerStart",markerUnits:1,markerWidth:1,mask:0,maskContentUnits:1,maskUnits:1,mathematical:0,mode:0,numOctaves:1,offset:0,opacity:0,operator:0,order:0,orient:0,orientation:0,origin:0,overflow:0,overlinePosition:1,"overline-position":"overlinePosition",overlineThickness:1,"overline-thickness":"overlineThickness",paintOrder:1,"paint-order":"paintOrder",panose1:0,"panose-1":"panose1",pathLength:1,patternContentUnits:1,patternTransform:1,patternUnits:1,pointerEvents:1,"pointer-events":"pointerEvents",points:0,pointsAtX:1,pointsAtY:1,pointsAtZ:1,prefix:0,preserveAlpha:1,preserveAspectRatio:1,primitiveUnits:1,property:0,r:0,radius:0,refX:1,refY:1,renderingIntent:1,"rendering-intent":"renderingIntent",repeatCount:1,repeatDur:1,requiredExtensions:1,requiredFeatures:1,resource:0,restart:0,result:0,results:0,rotate:0,rx:0,ry:0,scale:0,security:0,seed:0,shapeRendering:1,"shape-rendering":"shapeRendering",slope:0,spacing:0,specularConstant:1,specularExponent:1,speed:0,spreadMethod:1,startOffset:1,stdDeviation:1,stemh:0,stemv:0,stitchTiles:1,stopColor:1,"stop-color":"stopColor",stopOpacity:1,"stop-opacity":"stopOpacity",strikethroughPosition:1,"strikethrough-position":"strikethroughPosition",strikethroughThickness:1,"strikethrough-thickness":"strikethroughThickness",string:0,stroke:0,strokeDasharray:1,"stroke-dasharray":"strokeDasharray",strokeDashoffset:1,"stroke-dashoffset":"strokeDashoffset",strokeLinecap:1,"stroke-linecap":"strokeLinecap",strokeLinejoin:1,"stroke-linejoin":"strokeLinejoin",strokeMiterlimit:1,"stroke-miterlimit":"strokeMiterlimit",strokeWidth:1,"stroke-width":"strokeWidth",strokeOpacity:1,"stroke-opacity":"strokeOpacity",suppressContentEditableWarning:1,suppressHydrationWarning:1,surfaceScale:1,systemLanguage:1,tableValues:1,targetX:1,targetY:1,textAnchor:1,"text-anchor":"textAnchor",textDecoration:1,"text-decoration":"textDecoration",textLength:1,textRendering:1,"text-rendering":"textRendering",to:0,transform:0,typeof:0,u1:0,u2:0,underlinePosition:1,"underline-position":"underlinePosition",underlineThickness:1,"underline-thickness":"underlineThickness",unicode:0,unicodeBidi:1,"unicode-bidi":"unicodeBidi",unicodeRange:1,"unicode-range":"unicodeRange",unitsPerEm:1,"units-per-em":"unitsPerEm",unselectable:0,vAlphabetic:1,"v-alphabetic":"vAlphabetic",values:0,vectorEffect:1,"vector-effect":"vectorEffect",version:0,vertAdvY:1,"vert-adv-y":"vertAdvY",vertOriginX:1,"vert-origin-x":"vertOriginX",vertOriginY:1,"vert-origin-y":"vertOriginY",vHanging:1,"v-hanging":"vHanging",vIdeographic:1,"v-ideographic":"vIdeographic",viewBox:1,viewTarget:1,visibility:0,vMathematical:1,"v-mathematical":"vMathematical",vocab:0,widths:0,wordSpacing:1,"word-spacing":"wordSpacing",writingMode:1,"writing-mode":"writingMode",x1:0,x2:0,x:0,xChannelSelector:1,xHeight:1,"x-height":"xHeight",xlinkActuate:1,"xlink:actuate":"xlinkActuate",xlinkArcrole:1,"xlink:arcrole":"xlinkArcrole",xlinkHref:1,"xlink:href":"xlinkHref",xlinkRole:1,"xlink:role":"xlinkRole",xlinkShow:1,"xlink:show":"xlinkShow",xlinkTitle:1,"xlink:title":"xlinkTitle",xlinkType:1,"xlink:type":"xlinkType",xmlBase:1,"xml:base":"xmlBase",xmlLang:1,"xml:lang":"xmlLang",xmlns:0,"xml:space":"xmlSpace",xmlnsXlink:1,"xmlns:xlink":"xmlnsXlink",xmlSpace:1,y1:0,y2:0,y:0,yChannelSelector:1,z:0,zoomAndPan:1}},2088:function(t,e,r){"use strict";function n(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function i(t,e,r){return e&&n(t.prototype,e),r&&n(t,r),t}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){var n;n=r[e],e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,o=void 0;try{for(var s,a=t[Symbol.iterator]();!(n=(s=a.next()).done)&&(r.push(s.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==a.return||a.return()}finally{if(i)throw o}}return r}}(t,e)||u(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t){return function(t){if(Array.isArray(t))return c(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||u(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){if(t){if("string"==typeof t)return c(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return c(t,e)}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){return Object.getOwnPropertyNames(Object(t)).reduce(function(r,n){var i=Object.getOwnPropertyDescriptor(Object(t),n),o=Object.getOwnPropertyDescriptor(Object(e),n);return Object.defineProperty(r,n,o||i)},{})}function d(t){return"string"==typeof t}function f(t){return Array.isArray(t)}function p(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=h(e);return void 0!==r.types?t=r.types:void 0!==r.split&&(t=r.split),void 0!==t&&(r.types=(d(t)||f(t)?String(t):"").split(",").map(function(t){return String(t).trim()}).filter(function(t){return/((line)|(word)|(char))/i.test(t)})),(r.absolute||r.position)&&(r.absolute=r.absolute||/absolute/.test(e.position)),r}function m(t){var e=d(t)||f(t)?String(t):"";return{none:!e,lines:/line/i.test(e),words:/word/i.test(e),chars:/char/i.test(e)}}function g(t){return null!==t&&"object"==typeof t}function v(t){return g(t)&&/^(1|3|11)$/.test(t.nodeType)}function y(t){var e;return f(t)?t:null==t?[]:g(t)&&"number"==typeof(e=t.length)&&e>-1&&e%1==0?Array.prototype.slice.call(t):[t]}function b(t){var e=t;return d(t)&&(e=/^(#[a-z]\w+)$/.test(t.trim())?document.getElementById(t.trim().slice(1)):document.querySelectorAll(t)),y(e).reduce(function(t,e){return[].concat(l(t),l(y(e).filter(v)))},[])}r.d(e,{Z:function(){return q}}),function(){function t(){for(var t=arguments.length,e=0;e<t;e++){var r=e<0||arguments.length<=e?void 0:arguments[e];1===r.nodeType||11===r.nodeType?this.appendChild(r):this.appendChild(document.createTextNode(String(r)))}}function e(){for(;this.lastChild;)this.removeChild(this.lastChild);arguments.length&&this.append.apply(this,arguments)}function r(){for(var t=this.parentNode,e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];var i=r.length;if(t)for(i||t.removeChild(this);i--;){var o=r[i];"object"!=typeof o?o=this.ownerDocument.createTextNode(o):o.parentNode&&o.parentNode.removeChild(o),i?t.insertBefore(this.previousSibling,o):t.replaceChild(o,this)}}"undefined"==typeof Element||(Element.prototype.append||(Element.prototype.append=t,DocumentFragment.prototype.append=t),Element.prototype.replaceChildren||(Element.prototype.replaceChildren=e,DocumentFragment.prototype.replaceChildren=e),Element.prototype.replaceWith||(Element.prototype.replaceWith=r,DocumentFragment.prototype.replaceWith=r))}();var x=Object.entries,w="_splittype",T={},S=0;function E(t,e,r){if(!g(t))return console.warn("[data.set] owner is not an object"),null;var n=t[w]||(t[w]=++S),i=T[n]||(T[n]={});return void 0===r?e&&Object.getPrototypeOf(e)===Object.prototype&&(T[n]=s(s({},i),e)):void 0!==e&&(i[e]=r),r}function A(t,e){var r=g(t)?t[w]:null,n=r&&T[r]||{};return void 0===e?n:n[e]}function P(t){var e=t&&t[w];e&&(delete t[e],delete T[e])}var C="\ud800-\udfff",k="\\u0300-\\u036f\\ufe20-\\ufe23",R="\\u20d0-\\u20f0",_="\\ufe0e\\ufe0f",M="[".concat(k).concat(R,"]"),O="\ud83c[\udffb-\udfff]",D="(?:".concat(M,"|").concat(O,")"),j="[^".concat(C,"]"),L="(?:\ud83c[\udde6-\uddff]){2}",I="[\ud800-\udbff][\udc00-\udfff]",V="\\u200d",B="".concat(D,"?"),F="[".concat(_,"]?"),N="(?:"+V+"(?:"+[j,L,I].join("|")+")"+F+B+")*",U="(?:".concat(["".concat(j).concat(M,"?"),M,L,I,"[".concat(C,"]")].join("|"),"\n)"),z=RegExp("".concat(O,"(?=").concat(O,")|").concat(U).concat(F+B+N),"g"),W=[V,C,k,R,_],H=RegExp("[".concat(W.join(""),"]"));function Y(t,e){var r=document.createElement(t);return e&&Object.keys(e).forEach(function(t){var n=e[t],i=d(n)?n.trim():n;null!==i&&""!==i&&("children"===t?r.append.apply(r,l(y(i))):r.setAttribute(t,i))}),r}var X={splitClass:"",lineClass:"line",wordClass:"word",charClass:"char",types:["lines","words","chars"],absolute:!1,tagName:"div"},$=h(X,{}),q=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,t),this.isSplit=!1,this.settings=h($,p(r)),this.elements=b(e),this.split()}return i(t,null,[{key:"clearData",value:function(){Object.keys(T).forEach(function(t){delete T[t]})}},{key:"setDefaults",value:function(t){return $=h($,p(t)),X}},{key:"revert",value:function(t){b(t).forEach(function(t){var e=A(t),r=e.isSplit,n=e.html,i=e.cssWidth,o=e.cssHeight;r&&(t.innerHTML=n,t.style.width=i||"",t.style.height=o||"",P(t))})}},{key:"create",value:function(e,r){return new t(e,r)}},{key:"data",get:function(){return T}},{key:"defaults",get:function(){return $},set:function(t){$=h($,p(t))}}]),i(t,[{key:"split",value:function(t){var e=this;this.revert(),this.elements.forEach(function(t){E(t,"html",t.innerHTML)}),this.lines=[],this.words=[],this.chars=[];var r=[window.pageXOffset,window.pageYOffset];void 0!==t&&(this.settings=h(this.settings,p(t)));var n=m(this.settings.types);n.none||(this.elements.forEach(function(t){E(t,"isRoot",!0);var r=function t(e,r){var n,i,o,s,a,u,c,f=e.nodeType,p={words:[],chars:[]};if(!/(1|3|11)/.test(f))return p;if(3===f&&/\S/.test(e.nodeValue))return i=m((n=h(X,n=r)).types),o=n.tagName,s=e.nodeValue,a=document.createDocumentFragment(),u=[],c=[],/^\s/.test(s)&&a.append(" "),u=(function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return(t?String(t):"").trim().replace(/\s+/g," ").split(e)})(s).reduce(function(t,e,r,s){var u,h;return i.chars&&(h=(function(t){var e,r,n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return(t=null==(r=t)?"":String(r))&&d(t)&&!i&&(n=t,H.test(n))?(e=t,H.test(e)?e.match(z)||[]:e.split("")):t.split(i)})(e).map(function(t){var e=Y(o,{class:"".concat(n.splitClass," ").concat(n.charClass),style:"display: inline-block;",children:t});return E(e,"isChar",!0),c=[].concat(l(c),[e]),e})),i.words||i.lines?(E(u=Y(o,{class:"".concat(n.wordClass," ").concat(n.splitClass),style:"display: inline-block; ".concat(i.words&&n.absolute?"position: relative;":""),children:i.chars?h:e}),{isWord:!0,isWordStart:!0,isWordEnd:!0}),a.appendChild(u)):h.forEach(function(t){a.appendChild(t)}),r<s.length-1&&a.append(" "),i.words?t.concat(u):t},[]),/\s$/.test(s)&&a.append(" "),e.replaceWith(a),{words:u,chars:c};var g=y(e.childNodes);if(g.length&&(E(e,"isSplit",!0),!A(e).isRoot)){e.style.display="inline-block",e.style.position="relative";var v=e.nextSibling,b=e.previousSibling,x=e.textContent||"",w=v?v.textContent:" ",T=b?b.textContent:" ";E(e,{isWordEnd:/\s$/.test(x)||/^\s/.test(w),isWordStart:/^\s/.test(x)||/\s$/.test(T)})}return g.reduce(function(e,n){var i=t(n,r),o=i.words,s=i.chars;return{words:[].concat(l(e.words),l(o)),chars:[].concat(l(e.chars),l(s))}},p)}(t,e.settings),n=r.words,i=r.chars;e.words=[].concat(l(e.words),l(n)),e.chars=[].concat(l(e.chars),l(i))}),this.elements.forEach(function(t){if(n.lines||e.settings.absolute){var i,o,s,u,c,h,d,f,p,g,v,b,x,w,T,S,C,k=(c=m((i=e.settings).types),h=i.tagName,d=t.getElementsByTagName("*"),f=[],p=[],g=null,v=[],b=t.parentElement,x=t.nextElementSibling,w=document.createDocumentFragment(),S=(T=window.getComputedStyle(t)).textAlign,C=.2*parseFloat(T.fontSize),i.absolute&&(u={left:t.offsetLeft,top:t.offsetTop,width:t.offsetWidth},s=t.offsetWidth,o=t.offsetHeight,E(t,{cssWidth:t.style.width,cssHeight:t.style.height})),y(d).forEach(function(e){var n=e.parentElement===t,o=function(t,e,r,n){if(!r.absolute)return{top:e?t.offsetTop:null};var i=t.offsetParent,o=a(n,2),s=o[0],l=o[1],u=0,c=0;if(i&&i!==document.body){var h=i.getBoundingClientRect();u=h.x+s,c=h.y+l}var d=t.getBoundingClientRect(),f=d.width,p=d.height,m=d.x;return{width:f,height:p,top:d.y+l-c,left:m+s-u}}(e,n,i,r),s=o.width,l=o.height,u=o.top,h=o.left;!/^br$/i.test(e.nodeName)&&(c.lines&&n&&((null===g||u-g>=C)&&(g=u,f.push(p=[])),p.push(e)),i.absolute&&E(e,{top:u,left:h,width:s,height:l}))}),b&&b.removeChild(t),c.lines&&(v=f.map(function(t){var e=Y(h,{class:"".concat(i.splitClass," ").concat(i.lineClass),style:"display: block; text-align: ".concat(S,"; width: 100%;")});E(e,"isLine",!0);var r={height:0,top:1e4};return w.appendChild(e),t.forEach(function(t,n,i){var o=A(t),s=o.isWordEnd,a=o.top,l=o.height,u=i[n+1];r.height=Math.max(r.height,l),r.top=Math.min(r.top,a),e.appendChild(t),s&&A(u).isWordStart&&e.append(" ")}),i.absolute&&E(e,{height:r.height,top:r.top}),e}),c.words||function t(e){A(e).isWord?(P(e),e.replaceWith.apply(e,l(e.childNodes))):y(e.children).forEach(function(e){return t(e)})}(w),t.replaceChildren(w)),i.absolute&&(t.style.width="".concat(t.style.width||s,"px"),t.style.height="".concat(o,"px"),y(d).forEach(function(t){var e=A(t),r=e.isLine,n=e.top,i=e.left,o=e.width,s=e.height,a=A(t.parentElement),l=!r&&a.isLine;t.style.top="".concat(l?n-a.top:n,"px"),t.style.left=r?"".concat(u.left,"px"):"".concat(i-(l?u.left:0),"px"),t.style.height="".concat(s,"px"),t.style.width=r?"".concat(u.width,"px"):"".concat(o,"px"),t.style.position="absolute"})),b&&(x?b.insertBefore(t,x):b.appendChild(t)),v);e.lines=[].concat(l(e.lines),l(k))}}),this.isSplit=!0,window.scrollTo(r[0],r[1]),x(T).forEach(function(t){var e=a(t,2),r=e[0],n=e[1],i=n.isRoot,o=n.isSplit;i&&o||(T[r]=null,delete T[r])}))}},{key:"revert",value:function(){this.isSplit&&(this.lines=null,this.words=null,this.chars=null,this.isSplit=!1),t.revert(this.elements)}}]),t}()},3773:function(t,e,r){"use strict";var n=(this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}})(r(7564)),i=r(5553);function o(t,e){var r={};return t&&"string"==typeof t&&(0,n.default)(t,function(t,n){t&&n&&(r[(0,i.camelCase)(t,e)]=n)}),r}o.default=o,t.exports=o},5553:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.camelCase=void 0;var r=/^--[a-zA-Z0-9_-]+$/,n=/-([a-z])/g,i=/^[^-]+$/,o=/^-(webkit|moz|ms|o|khtml)-/,s=/^-(ms)-/,a=function(t,e){return e.toUpperCase()},l=function(t,e){return"".concat(e,"-")};e.camelCase=function(t,e){var u;return(void 0===e&&(e={}),!(u=t)||i.test(u)||r.test(u))?t:(t=t.toLowerCase(),(t=e.reactCompat?t.replace(s,l):t.replace(o,l)).replace(n,a))}},7564:function(t,e,r){"use strict";var n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var r=null;if(!t||"string"!=typeof t)return r;var n=(0,i.default)(t),o="function"==typeof e;return n.forEach(function(t){if("declaration"===t.type){var n=t.property,i=t.value;o?e(n,i,t):i&&((r=r||{})[n]=i)}}),r};var i=n(r(4958))},3398:function(t,e,r){"use strict";var n=r(357);r(6810);var i=r(2265),o=i&&"object"==typeof i&&"default"in i?i:{default:i},s=void 0!==n&&n.env&&!0,a=function(t){return"[object String]"===Object.prototype.toString.call(t)},l=function(){function t(t){var e=void 0===t?{}:t,r=e.name,n=void 0===r?"stylesheet":r,i=e.optimizeForSpeed,o=void 0===i?s:i;u(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",u("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var e=t.prototype;return e.setOptimizeForSpeed=function(t){u("boolean"==typeof t,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=t,this.inject()},e.isOptimizeForSpeed=function(){return this._optimizeForSpeed},e.inject=function(){var t=this;if(u(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(s||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(e,r){return"number"==typeof r?t._serverSheet.cssRules[r]={cssText:e}:t._serverSheet.cssRules.push({cssText:e}),r},deleteRule:function(e){t._serverSheet.cssRules[e]=null}}},e.getSheetForTag=function(t){if(t.sheet)return t.sheet;for(var e=0;e<document.styleSheets.length;e++)if(document.styleSheets[e].ownerNode===t)return document.styleSheets[e]},e.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},e.insertRule=function(t,e){if(u(a(t),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof e&&(e=this._serverSheet.cssRules.length),this._serverSheet.insertRule(t,e),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof e&&(e=r.cssRules.length);try{r.insertRule(t,e)}catch(e){return s||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[e];this._tags.push(this.makeStyleTag(this._name,t,n))}return this._rulesCount++},e.replaceRule=function(t,e){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(e.trim()||(e=this._deletedRulePlaceholder),!r.cssRules[t])return t;r.deleteRule(t);try{r.insertRule(e,t)}catch(n){s||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,t)}}else{var n=this._tags[t];u(n,"old rule at index `"+t+"` not found"),n.textContent=e}return t},e.deleteRule=function(t){if("undefined"==typeof window){this._serverSheet.deleteRule(t);return}if(this._optimizeForSpeed)this.replaceRule(t,"");else{var e=this._tags[t];u(e,"rule at index `"+t+"` not found"),e.parentNode.removeChild(e),this._tags[t]=null}},e.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(t){return t&&t.parentNode.removeChild(t)}),this._tags=[]):this._serverSheet.cssRules=[]},e.cssRules=function(){var t=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(e,r){return r?e=e.concat(Array.prototype.map.call(t.getSheetForTag(r).cssRules,function(e){return e.cssText===t._deletedRulePlaceholder?null:e})):e.push(null),e},[])},e.makeStyleTag=function(t,e,r){e&&u(a(e),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+t,""),e&&n.appendChild(document.createTextNode(e));var i=document.head||document.getElementsByTagName("head")[0];return r?i.insertBefore(n,r):i.appendChild(n),n},function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}(t.prototype,[{key:"length",get:function(){return this._rulesCount}}]),t}();function u(t,e){if(!t)throw Error("StyleSheet: "+e+".")}var c=function(t){for(var e=5381,r=t.length;r;)e=33*e^t.charCodeAt(--r);return e>>>0},h={};function d(t,e){if(!e)return"jsx-"+t;var r=String(e),n=t+r;return h[n]||(h[n]="jsx-"+c(t+"-"+r)),h[n]}function f(t,e){"undefined"==typeof window&&(e=e.replace(/\/style/gi,"\\/style"));var r=t+e;return h[r]||(h[r]=e.replace(/__jsx-style-dynamic-selector/g,t)),h[r]}var p=function(){function t(t){var e=void 0===t?{}:t,r=e.styleSheet,n=void 0===r?null:r,i=e.optimizeForSpeed,o=void 0!==i&&i;this._sheet=n||new l({name:"styled-jsx",optimizeForSpeed:o}),this._sheet.inject(),n&&"boolean"==typeof o&&(this._sheet.setOptimizeForSpeed(o),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var e=t.prototype;return e.add=function(t){var e=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(t.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(t,e){return t[e]=0,t},{}));var r=this.getIdAndRules(t),n=r.styleId,i=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var o=i.map(function(t){return e._sheet.insertRule(t)}).filter(function(t){return -1!==t});this._indices[n]=o,this._instancesCounts[n]=1},e.remove=function(t){var e=this,r=this.getIdAndRules(t).styleId;if(function(t,e){if(!t)throw Error("StyleSheetRegistry: "+e+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(t){return e._sheet.deleteRule(t)}),delete this._indices[r]),delete this._instancesCounts[r]}},e.update=function(t,e){this.add(e),this.remove(t)},e.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},e.cssRules=function(){var t=this,e=this._fromServer?Object.keys(this._fromServer).map(function(e){return[e,t._fromServer[e]]}):[],r=this._sheet.cssRules();return e.concat(Object.keys(this._indices).map(function(e){return[e,t._indices[e].map(function(t){return r[t].cssText}).join(t._optimizeForSpeed?"":"\n")]}).filter(function(t){return!!t[1]}))},e.styles=function(t){var e,r;return e=this.cssRules(),void 0===(r=t)&&(r={}),e.map(function(t){var e=t[0],n=t[1];return o.default.createElement("style",{id:"__"+e,key:"__"+e,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},e.getIdAndRules=function(t){var e=t.children,r=t.dynamic,n=t.id;if(r){var i=d(n,r);return{styleId:i,rules:Array.isArray(e)?e.map(function(t){return f(i,t)}):[f(i,e)]}}return{styleId:d(n),rules:Array.isArray(e)?e:[e]}},e.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(t,e){return t[e.id.slice(2)]=e,t},{})},t}(),m=i.createContext(null);m.displayName="StyleSheetContext";var g=o.default.useInsertionEffect||o.default.useLayoutEffect,v="undefined"!=typeof window?new p:void 0;function y(t){var e=v||i.useContext(m);return e&&("undefined"==typeof window?e.add(t):g(function(){return e.add(t),function(){e.remove(t)}},[t.id,String(t.dynamic)])),null}y.dynamic=function(t){return t.map(function(t){return d(t[0],t[1])}).join(" ")},e.style=y},8059:function(t,e,r){"use strict";t.exports=r(3398).style},3241:function(t,e,r){"use strict";r.d(e,{oO:function(){return o}});var n=r(2265),i=r(7797);function o(t=!0){let e=(0,n.useContext)(i.O);if(null===e)return[!0,null];let{isPresent:r,onExitComplete:o,register:s}=e,a=(0,n.useId)();(0,n.useEffect)(()=>{t&&s(a)},[t]);let l=(0,n.useCallback)(()=>t&&o&&o(a),[a,o,t]);return!r&&o?[!1,l]:[!0]}},5050:function(t,e,r){"use strict";r.d(e,{p:function(){return n}});let n=(0,r(2265).createContext)({})},9791:function(t,e,r){"use strict";r.d(e,{_:function(){return n}});let n=(0,r(2265).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},7797:function(t,e,r){"use strict";r.d(e,{O:function(){return n}});let n=(0,r(2265).createContext)(null)},1513:function(t,e,r){"use strict";let n;function i(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}r.d(e,{E:function(){return og}});let o=t=>Array.isArray(t);function s(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let n=0;n<r;n++)if(e[n]!==t[n])return!1;return!0}function a(t){return"string"==typeof t||Array.isArray(t)}function l(t){let e=[{},{}];return null==t||t.values.forEach((t,r)=>{e[0][r]=t.get(),e[1][r]=t.getVelocity()}),e}function u(t,e,r,n){if("function"==typeof e){let[i,o]=l(n);e=e(void 0!==r?r:t.custom,i,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[i,o]=l(n);e=e(void 0!==r?r:t.custom,i,o)}return e}function c(t,e,r){let n=t.getProps();return u(n,e,void 0!==r?r:n.custom,t)}let h=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],d=["initial",...h];function f(t){let e;return()=>(void 0===e&&(e=t()),e)}let p=f(()=>void 0!==window.ScrollTimeline);class m{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=e}attachTimeline(t,e){let r=this.animations.map(r=>p()&&r.attachTimeline?r.attachTimeline(t):"function"==typeof e?e(r):void 0);return()=>{r.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class g extends m{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function v(t,e){return t?t[e]||t.default||t:void 0}function y(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}function b(t){return"function"==typeof t}function x(t,e){t.timeline=e,t.onfinish=null}let w=t=>Array.isArray(t)&&"number"==typeof t[0],T={linearEasing:void 0},S=function(t,e){let r=f(t);return()=>{var t;return null!==(t=T[e])&&void 0!==t?t:r()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),E=(t,e,r)=>{let n=e-t;return 0===n?1:(r-t)/n},A=(t,e,r=10)=>{let n="",i=Math.max(Math.round(e/r),2);for(let e=0;e<i;e++)n+=t(E(0,i-1,e))+", ";return`linear(${n.substring(0,n.length-2)})`},P=([t,e,r,n])=>`cubic-bezier(${t}, ${e}, ${r}, ${n})`,C={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:P([0,.65,.55,1]),circOut:P([.55,0,1,.45]),backIn:P([.31,.01,.66,-.59]),backOut:P([.33,1.53,.69,.99])},k={x:!1,y:!1};function R(t,e){let r=function(t,e,r){if(t instanceof Element)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),n=new AbortController;return[r,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function _(t){return e=>{"touch"===e.pointerType||k.x||k.y||t(e)}}let M=(t,e)=>!!e&&(t===e||M(t,e.parentElement)),O=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,D=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),j=new WeakSet;function L(t){return e=>{"Enter"===e.key&&t(e)}}function I(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let V=(t,e)=>{let r=t.currentTarget;if(!r)return;let n=L(()=>{if(j.has(r))return;I(r,"down");let t=L(()=>{I(r,"up")});r.addEventListener("keyup",t,e),r.addEventListener("blur",()=>I(r,"cancel"),e)});r.addEventListener("keydown",n,e),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),e)};function B(t){return O(t)&&!(k.x||k.y)}let F=t=>1e3*t,N=t=>t/1e3,U=t=>t,z=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],W=new Set(z),H=new Set(["width","height","top","left","right","bottom",...z]),Y=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),X=t=>o(t)?t[t.length-1]||0:t,$={skipAnimations:!1,useManualTiming:!1},q=["read","resolveKeyframes","update","preRender","render","postRender"];function G(t,e){let r=!1,n=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>r=!0,s=q.reduce((t,e)=>(t[e]=function(t){let e=new Set,r=new Set,n=!1,i=!1,o=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1};function a(e){o.has(e)&&(l.schedule(e),t()),e(s)}let l={schedule:(t,i=!1,s=!1)=>{let a=s&&n?e:r;return i&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(s=t,n){i=!0;return}n=!0,[e,r]=[r,e],e.forEach(a),e.clear(),n=!1,i&&(i=!1,l.process(t))}};return l}(o),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=s,f=()=>{let o=$.useManualTiming?i.timestamp:performance.now();r=!1,i.delta=n?1e3/60:Math.max(Math.min(o-i.timestamp,40),1),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),h.process(i),d.process(i),i.isProcessing=!1,r&&e&&(n=!1,t(f))},p=()=>{r=!0,n=!0,i.isProcessing||t(f)};return{schedule:q.reduce((t,e)=>{let n=s[e];return t[e]=(t,e=!1,i=!1)=>(r||p(),n.schedule(t,e,i)),t},{}),cancel:t=>{for(let e=0;e<q.length;e++)s[q[e]].cancel(t)},state:i,steps:s}}let{schedule:K,cancel:Z,state:J,steps:Q}=G("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:U,!0);function tt(){n=void 0}let te={now:()=>(void 0===n&&te.set(J.isProcessing||$.useManualTiming?J.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(tt)}};function tr(t,e){-1===t.indexOf(e)&&t.push(e)}function tn(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class ti{constructor(){this.subscriptions=[]}add(t){return tr(this.subscriptions,t),()=>tn(this.subscriptions,t)}notify(t,e,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(t,e,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let to=t=>!isNaN(parseFloat(t)),ts={current:void 0};class ta{constructor(t,e={}){this.version="11.18.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let r=te.now();this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=te.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=to(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new ti);let r=this.events[t].add(e);return"change"===t?()=>{r(),K.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ts.current&&ts.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=te.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tl(t,e){return new ta(t,e)}let tu=t=>!!(t&&t.getVelocity);function tc(t,e){let r=t.getValue("willChange");if(tu(r)&&r.add)return r.add(e)}let th=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),td="data-"+th("framerAppearId"),tf={current:!1},tp=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function tm(t,e,r,n){if(t===e&&r===n)return U;let i=e=>(function(t,e,r,n,i){let o,s;let a=0;do(o=tp(s=e+(r-e)/2,n,i)-t)>0?r=s:e=s;while(Math.abs(o)>1e-7&&++a<12);return s})(e,0,1,t,r);return t=>0===t||1===t?t:tp(i(t),e,n)}let tg=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tv=t=>e=>1-t(1-e),ty=tm(.33,1.53,.69,.99),tb=tv(ty),tx=tg(tb),tw=t=>(t*=2)<1?.5*tb(t):.5*(2-Math.pow(2,-10*(t-1))),tT=t=>1-Math.sin(Math.acos(t)),tS=tv(tT),tE=tg(tT),tA=t=>/^0[^.\s]+$/u.test(t),tP=(t,e,r)=>r>e?e:r<t?t:r,tC={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tk={...tC,transform:t=>tP(0,1,t)},tR={...tC,default:1},t_=t=>Math.round(1e5*t)/1e5,tM=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tO=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tD=(t,e)=>r=>!!("string"==typeof r&&tO.test(r)&&r.startsWith(t)||e&&null!=r&&Object.prototype.hasOwnProperty.call(r,e)),tj=(t,e,r)=>n=>{if("string"!=typeof n)return n;let[i,o,s,a]=n.match(tM);return{[t]:parseFloat(i),[e]:parseFloat(o),[r]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},tL=t=>tP(0,255,t),tI={...tC,transform:t=>Math.round(tL(t))},tV={test:tD("rgb","red"),parse:tj("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:n=1})=>"rgba("+tI.transform(t)+", "+tI.transform(e)+", "+tI.transform(r)+", "+t_(tk.transform(n))+")"},tB={test:tD("#"),parse:function(t){let e="",r="",n="",i="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),n=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),n=t.substring(3,4),i=t.substring(4,5),e+=e,r+=r,n+=n,i+=i),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:tV.transform},tF=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tN=tF("deg"),tU=tF("%"),tz=tF("px"),tW=tF("vh"),tH=tF("vw"),tY={...tU,parse:t=>tU.parse(t)/100,transform:t=>tU.transform(100*t)},tX={test:tD("hsl","hue"),parse:tj("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:n=1})=>"hsla("+Math.round(t)+", "+tU.transform(t_(e))+", "+tU.transform(t_(r))+", "+t_(tk.transform(n))+")"},t$={test:t=>tV.test(t)||tB.test(t)||tX.test(t),parse:t=>tV.test(t)?tV.parse(t):tX.test(t)?tX.parse(t):tB.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tV.transform(t):tX.transform(t)},tq=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tG="number",tK="color",tZ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tJ(t){let e=t.toString(),r=[],n={color:[],number:[],var:[]},i=[],o=0,s=e.replace(tZ,t=>(t$.test(t)?(n.color.push(o),i.push(tK),r.push(t$.parse(t))):t.startsWith("var(")?(n.var.push(o),i.push("var"),r.push(t)):(n.number.push(o),i.push(tG),r.push(parseFloat(t))),++o,"${}")).split("${}");return{values:r,split:s,indexes:n,types:i}}function tQ(t){return tJ(t).values}function t0(t){let{split:e,types:r}=tJ(t),n=e.length;return t=>{let i="";for(let o=0;o<n;o++)if(i+=e[o],void 0!==t[o]){let e=r[o];e===tG?i+=t_(t[o]):e===tK?i+=t$.transform(t[o]):i+=t[o]}return i}}let t1=t=>"number"==typeof t?0:t,t2={test:function(t){var e,r;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(tM))||void 0===e?void 0:e.length)||0)+((null===(r=t.match(tq))||void 0===r?void 0:r.length)||0)>0},parse:tQ,createTransformer:t0,getAnimatableNone:function(t){let e=tQ(t);return t0(t)(e.map(t1))}},t5=new Set(["brightness","contrast","saturate","opacity"]);function t3(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=r.match(tM)||[];if(!n)return t;let i=r.replace(n,""),o=t5.has(e)?1:0;return n!==r&&(o*=100),e+"("+o+i+")"}let t6=/\b([a-z-]*)\(.*?\)/gu,t8={...t2,getAnimatableNone:t=>{let e=t.match(t6);return e?e.map(t3).join(" "):t}},t4={...tC,transform:Math.round},t9={borderWidth:tz,borderTopWidth:tz,borderRightWidth:tz,borderBottomWidth:tz,borderLeftWidth:tz,borderRadius:tz,radius:tz,borderTopLeftRadius:tz,borderTopRightRadius:tz,borderBottomRightRadius:tz,borderBottomLeftRadius:tz,width:tz,maxWidth:tz,height:tz,maxHeight:tz,top:tz,right:tz,bottom:tz,left:tz,padding:tz,paddingTop:tz,paddingRight:tz,paddingBottom:tz,paddingLeft:tz,margin:tz,marginTop:tz,marginRight:tz,marginBottom:tz,marginLeft:tz,backgroundPositionX:tz,backgroundPositionY:tz,rotate:tN,rotateX:tN,rotateY:tN,rotateZ:tN,scale:tR,scaleX:tR,scaleY:tR,scaleZ:tR,skew:tN,skewX:tN,skewY:tN,distance:tz,translateX:tz,translateY:tz,translateZ:tz,x:tz,y:tz,z:tz,perspective:tz,transformPerspective:tz,opacity:tk,originX:tY,originY:tY,originZ:tz,zIndex:t4,size:tz,fillOpacity:tk,strokeOpacity:tk,numOctaves:t4},t7={...t9,color:t$,backgroundColor:t$,outlineColor:t$,fill:t$,stroke:t$,borderColor:t$,borderTopColor:t$,borderRightColor:t$,borderBottomColor:t$,borderLeftColor:t$,filter:t8,WebkitFilter:t8},et=t=>t7[t];function ee(t,e){let r=et(t);return r!==t8&&(r=t2),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let er=new Set(["auto","none","0"]),en=t=>t===tC||t===tz,ei=(t,e)=>parseFloat(t.split(", ")[e]),eo=(t,e)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let i=n.match(/^matrix3d\((.+)\)$/u);if(i)return ei(i[1],e);{let e=n.match(/^matrix\((.+)\)$/u);return e?ei(e[1],t):0}},es=new Set(["x","y","z"]),ea=z.filter(t=>!es.has(t)),el={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:eo(4,13),y:eo(5,14)};el.translateX=el.x,el.translateY=el.y;let eu=new Set,ec=!1,eh=!1;function ed(){if(eh){let t=Array.from(eu).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),r=new Map;e.forEach(t=>{let e=function(t){let e=[];return ea.forEach(r=>{let n=t.getValue(r);void 0!==n&&(e.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),e}(t);e.length&&(r.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=r.get(t);e&&e.forEach(([e,r])=>{var n;null===(n=t.getValue(e))||void 0===n||n.set(r)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eh=!1,ec=!1,eu.forEach(t=>t.complete()),eu.clear()}function ef(){eu.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eh=!0)})}class ep{constructor(t,e,r,n,i,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=r,this.motionValue=n,this.element=i,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eu.add(this),ec||(ec=!0,K.read(ef),K.resolveKeyframes(ed))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:r,motionValue:n}=this;for(let i=0;i<t.length;i++)if(null===t[i]){if(0===i){let i=null==n?void 0:n.get(),o=t[t.length-1];if(void 0!==i)t[0]=i;else if(r&&e){let n=r.readValue(e,o);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=o),n&&void 0===i&&n.set(t[0])}else t[i]=t[i-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let em=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),eg=t=>e=>"string"==typeof e&&e.startsWith(t),ev=eg("--"),ey=eg("var(--"),eb=t=>!!ey(t)&&ex.test(t.split("/*")[0].trim()),ex=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ew=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eT=t=>e=>e.test(t),eS=[tC,tz,tU,tN,tH,tW,{test:t=>"auto"===t,parse:t=>t}],eE=t=>eS.find(eT(t));class eA extends ep{constructor(t,e,r,n,i){super(t,e,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:r}=this;if(!e||!e.current)return;super.readKeyframes();for(let r=0;r<t.length;r++){let n=t[r];if("string"==typeof n&&eb(n=n.trim())){let i=function t(e,r,n=1){U(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[i,o]=function(t){let e=ew.exec(t);if(!e)return[,];let[,r,n,i]=e;return[`--${null!=r?r:n}`,i]}(e);if(!i)return;let s=window.getComputedStyle(r).getPropertyValue(i);if(s){let t=s.trim();return em(t)?parseFloat(t):t}return eb(o)?t(o,r,n+1):o}(n,e.current);void 0!==i&&(t[r]=i),r===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!H.has(r)||2!==t.length)return;let[n,i]=t,o=eE(n),s=eE(i);if(o!==s){if(en(o)&&en(s))for(let e=0;e<t.length;e++){let r=t[e];"string"==typeof r&&(t[e]=parseFloat(r))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,r=[];for(let e=0;e<t.length;e++){var n;("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tA(n))&&r.push(e)}r.length&&function(t,e,r){let n,i=0;for(;i<t.length&&!n;){let e=t[i];"string"==typeof e&&!er.has(e)&&tJ(e).values.length&&(n=t[i]),i++}if(n&&r)for(let i of e)t[i]=ee(r,n)}(t,r,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:r}=this;if(!t||!t.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=el[r](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(r,n).jump(n,!1)}measureEndState(){var t;let{element:e,name:r,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let i=e.getValue(r);i&&i.jump(this.measuredOrigin,!1);let o=n.length-1,s=n[o];n[o]=el[r](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,r])=>{e.getValue(t).set(r)}),this.resolveNoneKeyframes()}}let eP=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t2.test(t)||"0"===t)&&!t.startsWith("url(")),eC=t=>null!==t;function ek(t,{repeat:e,repeatType:r="loop"},n){let i=t.filter(eC),o=e&&"loop"!==r&&e%2==1?0:i.length-1;return o&&void 0!==n?n:i[o]}class eR{constructor({autoplay:t=!0,delay:e=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:o="loop",...s}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=te.now(),this.options={autoplay:t,delay:e,type:r,repeat:n,repeatDelay:i,repeatType:o,...s},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ef(),ed()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=te.now(),this.hasAttemptedResolve=!0;let{name:r,type:n,velocity:i,delay:o,onComplete:s,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,r,n){let i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;let o=t[t.length-1],s=eP(i,e),a=eP(o,e);return U(s===a,`You are trying to animate ${e} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!s&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let r=0;r<t.length;r++)if(t[r]!==e)return!0}(t)||("spring"===r||b(r))&&n)}(t,r,n,i)){if(tf.current||!o){null==a||a(ek(t,this.options,e)),null==s||s(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let e_=(t,e,r)=>t+(e-t)*r;function eM(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function eO(t,e){return r=>r>0?e:t}let eD=(t,e,r)=>{let n=t*t,i=r*(e*e-n)+n;return i<0?0:Math.sqrt(i)},ej=[tB,tV,tX],eL=t=>ej.find(e=>e.test(t));function eI(t){let e=eL(t);if(U(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let r=e.parse(t);return e===tX&&(r=function({hue:t,saturation:e,lightness:r,alpha:n}){t/=360,r/=100;let i=0,o=0,s=0;if(e/=100){let n=r<.5?r*(1+e):r+e-r*e,a=2*r-n;i=eM(a,n,t+1/3),o=eM(a,n,t),s=eM(a,n,t-1/3)}else i=o=s=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:n}}(r)),r}let eV=(t,e)=>{let r=eI(t),n=eI(e);if(!r||!n)return eO(t,e);let i={...r};return t=>(i.red=eD(r.red,n.red,t),i.green=eD(r.green,n.green,t),i.blue=eD(r.blue,n.blue,t),i.alpha=e_(r.alpha,n.alpha,t),tV.transform(i))},eB=(t,e)=>r=>e(t(r)),eF=(...t)=>t.reduce(eB),eN=new Set(["none","hidden"]);function eU(t,e){return r=>e_(t,e,r)}function ez(t){return"number"==typeof t?eU:"string"==typeof t?eb(t)?eO:t$.test(t)?eV:eY:Array.isArray(t)?eW:"object"==typeof t?t$.test(t)?eV:eH:eO}function eW(t,e){let r=[...t],n=r.length,i=t.map((t,r)=>ez(t)(t,e[r]));return t=>{for(let e=0;e<n;e++)r[e]=i[e](t);return r}}function eH(t,e){let r={...t,...e},n={};for(let i in r)void 0!==t[i]&&void 0!==e[i]&&(n[i]=ez(t[i])(t[i],e[i]));return t=>{for(let e in n)r[e]=n[e](t);return r}}let eY=(t,e)=>{let r=t2.createTransformer(e),n=tJ(t),i=tJ(e);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?eN.has(t)&&!i.values.length||eN.has(e)&&!n.values.length?eN.has(t)?r=>r<=0?t:e:r=>r>=1?e:t:eF(eW(function(t,e){var r;let n=[],i={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){let s=e.types[o],a=t.indexes[s][i[s]],l=null!==(r=t.values[a])&&void 0!==r?r:0;n[o]=l,i[s]++}return n}(n,i),i.values),r):(U(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eO(t,e))};function eX(t,e,r){return"number"==typeof t&&"number"==typeof e&&"number"==typeof r?e_(t,e,r):ez(t)(t,e)}function e$(t,e,r){var n,i;let o=Math.max(e-5,0);return n=r-t(o),(i=e-o)?1e3/i*n:0}let eq={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eG(t,e){return t*Math.sqrt(1-e*e)}let eK=["duration","bounce"],eZ=["stiffness","damping","mass"];function eJ(t,e){return e.some(e=>void 0!==t[e])}function eQ(t=eq.visualDuration,e=eq.bounce){let r;let n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:i,restDelta:o}=n,s=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:h,duration:d,velocity:f,isResolvedFromDuration:p}=function(t){let e={velocity:eq.velocity,stiffness:eq.stiffness,damping:eq.damping,mass:eq.mass,isResolvedFromDuration:!1,...t};if(!eJ(t,eZ)&&eJ(t,eK)){if(t.visualDuration){let r=2*Math.PI/(1.2*t.visualDuration),n=r*r,i=2*tP(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:eq.mass,stiffness:n,damping:i}}else{let r=function({duration:t=eq.duration,bounce:e=eq.bounce,velocity:r=eq.velocity,mass:n=eq.mass}){let i,o;U(t<=F(eq.maxDuration),"Spring duration must be 10 seconds or less");let s=1-e;s=tP(eq.minDamping,eq.maxDamping,s),t=tP(eq.minDuration,eq.maxDuration,N(t)),s<1?(i=e=>{let n=e*s,i=n*t;return .001-(n-r)/eG(e,s)*Math.exp(-i)},o=e=>{let n=e*s*t,o=Math.pow(s,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=eG(Math.pow(e,2),s);return(n*r+r-o)*a*(-i(e)+.001>0?-1:1)/l}):(i=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),o=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let n=r;for(let r=1;r<12;r++)n-=t(n)/e(n);return n}(i,o,5/t);if(t=F(t),isNaN(a))return{stiffness:eq.stiffness,damping:eq.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*s*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...r,mass:eq.mass}).isResolvedFromDuration=!0}}return e}({...n,velocity:-N(n.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*h)),v=a-s,b=N(Math.sqrt(u/h)),x=5>Math.abs(v);if(i||(i=x?eq.restSpeed.granular:eq.restSpeed.default),o||(o=x?eq.restDelta.granular:eq.restDelta.default),g<1){let t=eG(b,g);r=e=>a-Math.exp(-g*b*e)*((m+g*b*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)r=t=>a-Math.exp(-b*t)*(v+(m+b*v)*t);else{let t=b*Math.sqrt(g*g-1);r=e=>{let r=Math.exp(-g*b*e),n=Math.min(t*e,300);return a-r*((m+g*b*v)*Math.sinh(n)+t*v*Math.cosh(n))/t}}let w={calculatedDuration:p&&d||null,next:t=>{let e=r(t);if(p)l.done=t>=d;else{let n=0;g<1&&(n=0===t?F(m):e$(r,t,e));let s=Math.abs(n)<=i,u=Math.abs(a-e)<=o;l.done=s&&u}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(y(w),2e4),e=A(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function e0({keyframes:t,velocity:e=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let h,d;let f=t[0],p={done:!1,value:f},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,v=r*e,y=f+v,b=void 0===s?y:s(y);b!==y&&(v=b-f);let x=t=>-v*Math.exp(-t/n),w=t=>b+x(t),T=t=>{let e=x(t),r=w(t);p.done=Math.abs(e)<=u,p.value=p.done?b:r},S=t=>{m(p.value)&&(h=t,d=eQ({keyframes:[p.value,g(p.value)],velocity:e$(w,t,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return S(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==h||(e=!0,T(t),S(t)),void 0!==h&&t>=h)?d.next(t-h):(e||T(t),p)}}}let e1=tm(.42,0,1,1),e2=tm(0,0,.58,1),e5=tm(.42,0,.58,1),e3=t=>Array.isArray(t)&&"number"!=typeof t[0],e6={linear:U,easeIn:e1,easeInOut:e5,easeOut:e2,circIn:tT,circInOut:tE,circOut:tS,backIn:tb,backInOut:tx,backOut:ty,anticipate:tw},e8=t=>{if(w(t)){U(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,r,n,i]=t;return tm(e,r,n,i)}return"string"==typeof t?(U(void 0!==e6[t],`Invalid easing type '${t}'`),e6[t]):t};function e4({duration:t=300,keyframes:e,times:r,ease:n="easeInOut"}){let i=e3(n)?n.map(e8):e8(n),o={done:!1,value:e[0]},s=function(t,e,{clamp:r=!0,ease:n,mixer:i}={}){let o=t.length;if(U(o===e.length,"Both input and output ranges must be the same length"),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];let s=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,r){let n=[],i=r||eX,o=t.length-1;for(let r=0;r<o;r++){let o=i(t[r],t[r+1]);e&&(o=eF(Array.isArray(e)?e[r]||U:e,o)),n.push(o)}return n}(e,n,i),l=a.length,u=r=>{if(s&&r<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(r<t[n+1]);n++);let i=E(t[n],t[n+1],r);return a[n](i)};return r?e=>u(tP(t[0],t[o-1],e)):u}((r&&r.length===e.length?r:function(t){let e=[0];return function(t,e){let r=t[t.length-1];for(let n=1;n<=e;n++){let i=E(0,e,n);t.push(e_(r,1,i))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(i)?i:e.map(()=>i||e5).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=s(e),o.done=e>=t,o)}}let e9=t=>{let e=({timestamp:e})=>t(e);return{start:()=>K.update(e,!0),stop:()=>Z(e),now:()=>J.isProcessing?J.timestamp:te.now()}},e7={decay:e0,inertia:e0,tween:e4,keyframes:e4,spring:eQ},rt=t=>t/100;class re extends eR{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:r,element:n,keyframes:i}=this.options,o=(null==n?void 0:n.KeyframeResolver)||ep;this.resolver=new o(i,(t,e)=>this.onKeyframesResolved(t,e),e,r,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,r;let{type:n="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:s,velocity:a=0}=this.options,l=b(n)?n:e7[n]||e4;l!==e4&&"number"!=typeof t[0]&&(e=eF(rt,eX(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===s&&(r=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=y(u));let{calculatedDuration:c}=u,h=c+o;return{generator:u,mirroredGenerator:r,mapPercentToKeyframes:e,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(i+1)-o}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:r}=this;if(!r){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:n,generator:i,mirroredGenerator:o,mapPercentToKeyframes:s,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=r;if(null===this.startTime)return i.next(0);let{delay:h,repeat:d,repeatType:f,repeatDelay:p,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let g=this.currentTime-h*(this.speed>=0?1:-1),v=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,b=i;if(d){let t=Math.min(this.currentTime,u)/c,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,d+1))%2&&("reverse"===f?(r=1-r,p&&(r-=p/c)):"mirror"===f&&(b=o)),y=tP(0,1,r)*c}let x=v?{done:!1,value:a[0]}:b.next(y);s&&(x.value=s(x.value));let{done:w}=x;v||null===l||(w=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&void 0!==n&&(x.value=ek(a,this.options,n)),m&&m(x.value),T&&this.finish(),x}get duration(){let{resolved:t}=this;return t?N(t.calculatedDuration):0}get time(){return N(this.currentTime)}set time(t){t=F(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=N(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e9,onPlay:e,startTime:r}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=null!=r?r:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let rr=new Set(["opacity","clipPath","filter","transform"]),rn=f(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ri={anticipate:tw,backInOut:tx,circInOut:tE};class ro extends eR{constructor(t){super(t);let{name:e,motionValue:r,element:n,keyframes:i}=this.options;this.resolver=new eA(i,(t,e)=>this.onKeyframesResolved(t,e),e,r,n),this.resolver.scheduleResolve()}initPlayback(t,e){var r,n;let{duration:i=300,times:o,ease:s,type:a,motionValue:l,name:u,startTime:c}=this.options;if(!(null===(r=l.owner)||void 0===r?void 0:r.current))return!1;if("string"==typeof s&&S()&&s in ri&&(s=ri[s]),b((n=this.options).type)||"spring"===n.type||!function t(e){return!!("function"==typeof e&&S()||!e||"string"==typeof e&&(e in C||S())||w(e)||Array.isArray(e)&&e.every(t))}(n.ease)){let{onComplete:e,onUpdate:r,motionValue:n,element:l,...u}=this.options,c=function(t,e){let r=new re({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:t[0]},i=[],o=0;for(;!n.done&&o<2e4;)i.push((n=r.sample(o)).value),o+=10;return{times:void 0,keyframes:i,duration:o-10,ease:"linear"}}(t,u);1===(t=c.keyframes).length&&(t[1]=t[0]),i=c.duration,o=c.times,s=c.ease,a="keyframes"}let h=function(t,e,r,{delay:n=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeInOut",times:l}={}){let u={[e]:r};l&&(u.offset=l);let c=function t(e,r){if(e)return"function"==typeof e&&S()?A(e,r):w(e)?P(e):Array.isArray(e)?e.map(e=>t(e,r)||C.easeOut):C[e]}(a,i);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:n,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(l.owner.current,u,t,{...this.options,duration:i,times:o,ease:s});return h.startTime=null!=c?c:this.calcStartTime(),this.pendingTimeline?(x(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:r}=this.options;l.set(ek(t,this.options,e)),r&&r(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:i,times:o,type:a,ease:s,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return N(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return N(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:r}=e;r.currentTime=F(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:r}=e;r.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return U;let{animation:r}=e;x(r,t)}else this.pendingTimeline=t;return U}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:r,duration:n,type:i,ease:o,times:s}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,c=new re({...u,keyframes:r,duration:n,type:i,ease:o,times:s,isGenerator:!0}),h=F(this.time);t.setWithVelocity(c.sample(h-10).value,c.sample(h).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:r,repeatDelay:n,repeatType:i,damping:o,type:s}=t;return rn()&&r&&rr.has(r)&&e&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate&&!n&&"mirror"!==i&&0!==o&&"inertia"!==s}}let rs={type:"spring",stiffness:500,damping:25,restSpeed:10},ra=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),rl={type:"keyframes",duration:.8},ru={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rc=(t,{keyframes:e})=>e.length>2?rl:W.has(t)?t.startsWith("scale")?ra(e[1]):rs:ru,rh=(t,e,r,n={},i,o)=>s=>{let a=v(n,t)||{},l=a.delay||n.delay||0,{elapsed:u=0}=n;u-=F(l);let c={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};!function({when:t,delay:e,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&(c={...c,...rc(t,c)}),c.duration&&(c.duration=F(c.duration)),c.repeatDelay&&(c.repeatDelay=F(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(h=!0)),(tf.current||$.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),h&&!o&&void 0!==e.get()){let t=ek(c.keyframes,a);if(void 0!==t)return K.update(()=>{c.onUpdate(t),c.onComplete()}),new g([])}return!o&&ro.supports(c)?new ro(c):new re(c)};function rd(t,e,{delay:r=0,transitionOverride:n,type:i}={}){var o;let{transition:s=t.getDefaultTransition(),transitionEnd:a,...l}=e;n&&(s=n);let u=[],h=i&&t.animationState&&t.animationState.getState()[i];for(let e in l){let n=t.getValue(e,null!==(o=t.latestValues[e])&&void 0!==o?o:null),i=l[e];if(void 0===i||h&&function({protectedKeys:t,needsAnimating:e},r){let n=t.hasOwnProperty(r)&&!0!==e[r];return e[r]=!1,n}(h,e))continue;let a={delay:r,...v(s||{},e)},c=!1;if(window.MotionHandoffAnimation){let r=t.props[td];if(r){let t=window.MotionHandoffAnimation(r,e,K);null!==t&&(a.startTime=t,c=!0)}}tc(t,e),n.start(rh(e,n,i,t.shouldReduceMotion&&H.has(e)?{type:!1}:a,t,c));let d=n.animation;d&&u.push(d)}return a&&Promise.all(u).then(()=>{K.update(()=>{a&&function(t,e){let{transitionEnd:r={},transition:n={},...i}=c(t,e)||{};for(let e in i={...i,...r}){let r=X(i[e]);t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,tl(r))}}(t,a)})}),u}function rf(t,e,r={}){var n;let i=c(t,e,"exit"===r.type?null===(n=t.presenceContext)||void 0===n?void 0:n.custom:void 0),{transition:o=t.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(o=r.transitionOverride);let s=i?()=>Promise.all(rd(t,i,r)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:i=0,staggerChildren:s,staggerDirection:a}=o;return function(t,e,r=0,n=0,i=1,o){let s=[],a=(t.variantChildren.size-1)*n,l=1===i?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(rp).forEach((t,n)=>{t.notify("AnimationStart",e),s.push(rf(t,e,{...o,delay:r+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(s)}(t,e,i+n,s,a,r)}:()=>Promise.resolve(),{when:l}=o;if(!l)return Promise.all([s(),a(r.delay)]);{let[t,e]="beforeChildren"===l?[s,a]:[a,s];return t().then(()=>e())}}function rp(t,e){return t.sortNodePosition(e)}let rm=d.length,rg=[...h].reverse(),rv=h.length;function ry(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rb(){return{animate:ry(!0),whileInView:ry(),whileHover:ry(),whileTap:ry(),whileDrag:ry(),whileFocus:ry(),exit:ry()}}class rx{constructor(t){this.isMounted=!1,this.node=t}update(){}}class rw extends rx{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:r})=>(function(t,e,r={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>rf(t,e,r)));else if("string"==typeof e)n=rf(t,e,r);else{let i="function"==typeof e?c(t,e,r.custom):e;n=Promise.all(rd(t,i,r))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,r))),r=rb(),n=!0,l=e=>(r,n)=>{var i;let o=c(t,n,"exit"===e?null===(i=t.presenceContext)||void 0===i?void 0:i.custom:void 0);if(o){let{transition:t,transitionEnd:e,...n}=o;r={...r,...n,...e}}return r};function u(u){let{props:c}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let r=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(r.initial=e.props.initial),r}let r={};for(let t=0;t<rm;t++){let n=d[t],i=e.props[n];(a(i)||!1===i)&&(r[n]=i)}return r}(t.parent)||{},f=[],p=new Set,m={},g=1/0;for(let e=0;e<rv;e++){var v;let d=rg[e],y=r[d],b=void 0!==c[d]?c[d]:h[d],x=a(b),w=d===u?y.isActive:null;!1===w&&(g=e);let T=b===h[d]&&b!==c[d]&&x;if(T&&n&&t.manuallyAnimateOnMount&&(T=!1),y.protectedKeys={...m},!y.isActive&&null===w||!b&&!y.prevProp||i(b)||"boolean"==typeof b)continue;let S=(v=y.prevProp,"string"==typeof b?b!==v:!!Array.isArray(b)&&!s(b,v)),E=S||d===u&&y.isActive&&!T&&x||e>g&&x,A=!1,P=Array.isArray(b)?b:[b],C=P.reduce(l(d),{});!1===w&&(C={});let{prevResolvedValues:k={}}=y,R={...k,...C},_=e=>{E=!0,p.has(e)&&(A=!0,p.delete(e)),y.needsAnimating[e]=!0;let r=t.getValue(e);r&&(r.liveStyle=!1)};for(let t in R){let e=C[t],r=k[t];if(!m.hasOwnProperty(t))(o(e)&&o(r)?s(e,r):e===r)?void 0!==e&&p.has(t)?_(t):y.protectedKeys[t]=!0:null!=e?_(t):p.add(t)}y.prevProp=b,y.prevResolvedValues=C,y.isActive&&(m={...m,...C}),n&&t.blockInitialAnimation&&(E=!1);let M=!(T&&S)||A;E&&M&&f.push(...P.map(t=>({animation:t,options:{type:d}})))}if(p.size){let e={};p.forEach(r=>{let n=t.getBaseTarget(r),i=t.getValue(r);i&&(i.liveStyle=!0),e[r]=null!=n?n:null}),f.push({animation:e})}let y=!!f.length;return n&&(!1===c.initial||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(f):Promise.resolve()}return{animateChanges:u,setActive:function(e,n){var i;if(r[e].isActive===n)return Promise.resolve();null===(i=t.variantChildren)||void 0===i||i.forEach(t=>{var r;return null===(r=t.animationState)||void 0===r?void 0:r.setActive(e,n)}),r[e].isActive=n;let o=u(e);for(let t in r)r[t].protectedKeys={};return o},setAnimateFunction:function(r){e=r(t)},getState:()=>r,reset:()=>{r=rb(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();i(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let rT=0;class rS extends rx{constructor(){super(...arguments),this.id=rT++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function rE(t,e,r,n={passive:!0}){return t.addEventListener(e,r,n),()=>t.removeEventListener(e,r)}function rA(t){return{point:{x:t.pageX,y:t.pageY}}}let rP=t=>e=>O(e)&&t(e,rA(e));function rC(t,e,r,n){return rE(t,e,rP(r),n)}let rk=(t,e)=>Math.abs(t-e);class rR{constructor(t,e,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=rO(this.lastMoveEventInfo,this.history),n=null!==this.startEvent,i=(t=r.offset,e={x:0,y:0},Math.sqrt(rk(t.x,e.x)**2+rk(t.y,e.y)**2)>=3);if(!n&&!i)return;let{point:o}=r,{timestamp:s}=J;this.history.push({...o,timestamp:s});let{onStart:a,onMove:l}=this.handlers;n||(a&&a(this.lastMoveEvent,r),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,r)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=r_(e,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rO("pointercancel"===t.type?this.lastMoveEventInfo:r_(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,o),n&&n(t,o)},!O(t))return;this.dragSnapToOrigin=i,this.handlers=e,this.transformPagePoint=r,this.contextWindow=n||window;let o=r_(rA(t),this.transformPagePoint),{point:s}=o,{timestamp:a}=J;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,rO(o,this.history)),this.removeListeners=eF(rC(this.contextWindow,"pointermove",this.handlePointerMove),rC(this.contextWindow,"pointerup",this.handlePointerUp),rC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function r_(t,e){return e?{point:e(t.point)}:t}function rM(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rO({point:t},e){return{point:t,delta:rM(t,rD(e)),offset:rM(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,n=null,i=rD(t);for(;r>=0&&(n=t[r],!(i.timestamp-n.timestamp>F(.1)));)r--;if(!n)return{x:0,y:0};let o=N(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(e,0)}}function rD(t){return t[t.length-1]}function rj(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function rL(t){return t.max-t.min}function rI(t,e,r,n=.5){t.origin=n,t.originPoint=e_(e.min,e.max,t.origin),t.scale=rL(r)/rL(e),t.translate=e_(r.min,r.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function rV(t,e,r,n){rI(t.x,e.x,r.x,n?n.originX:void 0),rI(t.y,e.y,r.y,n?n.originY:void 0)}function rB(t,e,r){t.min=r.min+e.min,t.max=t.min+rL(e)}function rF(t,e,r){t.min=e.min-r.min,t.max=t.min+rL(e)}function rN(t,e,r){rF(t.x,e.x,r.x),rF(t.y,e.y,r.y)}function rU(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function rz(t,e){let r=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,n]=[n,r]),{min:r,max:n}}function rW(t,e,r){return{min:rH(t,e),max:rH(t,r)}}function rH(t,e){return"number"==typeof t?t:t[e]||0}let rY=()=>({translate:0,scale:1,origin:0,originPoint:0}),rX=()=>({x:rY(),y:rY()}),r$=()=>({min:0,max:0}),rq=()=>({x:r$(),y:r$()});function rG(t){return[t("x"),t("y")]}function rK({top:t,left:e,right:r,bottom:n}){return{x:{min:e,max:r},y:{min:t,max:n}}}function rZ(t){return void 0===t||1===t}function rJ({scale:t,scaleX:e,scaleY:r}){return!rZ(t)||!rZ(e)||!rZ(r)}function rQ(t){return rJ(t)||r0(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function r0(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function r1(t,e,r,n,i){return void 0!==i&&(t=n+i*(t-n)),n+r*(t-n)+e}function r2(t,e=0,r=1,n,i){t.min=r1(t.min,e,r,n,i),t.max=r1(t.max,e,r,n,i)}function r5(t,{x:e,y:r}){r2(t.x,e.translate,e.scale,e.originPoint),r2(t.y,r.translate,r.scale,r.originPoint)}function r3(t,e){t.min=t.min+e,t.max=t.max+e}function r6(t,e,r,n,i=.5){let o=e_(t.min,t.max,i);r2(t,e,r,o,n)}function r8(t,e){r6(t.x,e.x,e.scaleX,e.scale,e.originX),r6(t.y,e.y,e.scaleY,e.scale,e.originY)}function r4(t,e){return rK(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let r9=({current:t})=>t?t.ownerDocument.defaultView:null,r7=new WeakMap;class nt{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rq(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new rR(t,{onSessionStart:t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(rA(t).point)},onStart:(t,e)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===r||"y"===r?k[r]?null:(k[r]=!0,()=>{k[r]=!1}):k.x||k.y?null:(k.x=k.y=!0,()=>{k.x=k.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rG(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tU.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[t];if(n){let t=rL(n);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),i&&K.postRender(()=>i(t,e)),tc(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:s}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",e.point,s),this.updateAxis("y",e.point,s),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>rG(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:r9(this.visualElement)})}stop(t,e){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&K.postRender(()=>i(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:n}=this.getProps();if(!r||!ne(t,n,this.currentDirection))return;let i=this.getAxisMotionValue(t),o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:r},n){return void 0!==e&&t<e?t=n?e_(e,t,n.min):Math.max(t,e):void 0!==r&&t>r&&(t=n?e_(r,t,n.max):Math.min(t,r)),t}(o,this.constraints[t],this.elastic[t])),i.set(o)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,i=this.constraints;e&&rj(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,{top:e,left:r,bottom:n,right:i}){return{x:rU(t.x,r,i),y:rU(t.y,e,n)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:rW(t,"left","right"),y:rW(t,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rG(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!rj(e))return!1;let n=e.current;U(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(t,e,r){let n=r4(t,r),{scroll:i}=e;return i&&(r3(n.x,i.offset.x),r3(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),s={x:rz((t=i.layout.layoutBox).x,o.x),y:rz(t.y,o.y)};if(r){let t=r(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(s));this.hasMutatedConstraints=!!t,t&&(s=rK(t))}return s}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(rG(s=>{if(!ne(s,e,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?t[s]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return tc(this.visualElement,t),r.start(rh(t,r,0,e,this.visualElement,!1))}stopAnimation(){rG(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){rG(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){rG(e=>{let{drag:r}=this.getProps();if(!ne(e,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(e);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[e];i.set(t[e]-e_(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!rj(e)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};rG(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let r=e.get();n[t]=function(t,e){let r=.5,n=rL(t),i=rL(e);return i>n?r=E(e.min,e.max-n,t.min):n>i&&(r=E(t.min,t.max-i,e.min)),tP(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rG(e=>{if(!ne(e,t,null))return;let r=this.getAxisMotionValue(e),{min:i,max:o}=this.constraints[e];r.set(e_(i,o,n[e]))})}addListeners(){if(!this.visualElement.current)return;r7.set(this.visualElement,this);let t=rC(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();rj(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),K.read(e);let i=rE(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(rG(e=>{let r=this.getAxisMotionValue(e);r&&(this.originPoint[e]+=t[e].translate,r.set(r.get()+t[e].translate))}),this.visualElement.render())});return()=>{i(),t(),n(),o&&o()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function ne(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class nr extends rx{constructor(t){super(t),this.removeGroupControls=U,this.removeListeners=U,this.controls=new nt(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||U}unmount(){this.removeGroupControls(),this.removeListeners()}}let nn=t=>(e,r)=>{t&&K.postRender(()=>t(e,r))};class ni extends rx{constructor(){super(...arguments),this.removePointerDownListener=U}onPointerDown(t){this.session=new rR(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:r9(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:nn(t),onStart:nn(e),onMove:r,onEnd:(t,e)=>{delete this.session,n&&K.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=rC(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var no,ns,na,nl=r(7437),nu=r(2265),nc=r(3241),nh=r(5050);let nd=(0,nu.createContext)({}),nf={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function np(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let nm={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tz.test(t))return t;t=parseFloat(t)}let r=np(t,e.target.x),n=np(t,e.target.y);return`${r}% ${n}%`}},ng={},{schedule:nv,cancel:ny}=G(queueMicrotask,!1);class nb extends nu.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=t;Object.assign(ng,nw),i&&(e.group&&e.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nf.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:n,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,n||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent===i||(i?o.promote():o.relegate()||K.postRender(()=>{let t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),nv.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nx(t){let[e,r]=(0,nc.oO)(),n=(0,nu.useContext)(nh.p);return(0,nl.jsx)(nb,{...t,layoutGroup:n,switchLayoutGroup:(0,nu.useContext)(nd),isPresent:e,safeToRemove:r})}let nw={borderRadius:{...nm,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nm,borderTopRightRadius:nm,borderBottomLeftRadius:nm,borderBottomRightRadius:nm,boxShadow:{correct:(t,{treeScale:e,projectionDelta:r})=>{let n=t2.parse(t);if(n.length>5)return t;let i=t2.createTransformer(t),o="number"!=typeof n[0]?1:0,s=r.x.scale*e.x,a=r.y.scale*e.y;n[0+o]/=s,n[1+o]/=a;let l=e_(s,a,.5);return"number"==typeof n[2+o]&&(n[2+o]/=l),"number"==typeof n[3+o]&&(n[3+o]/=l),i(n)}}},nT=(t,e)=>t.depth-e.depth;class nS{constructor(){this.children=[],this.isDirty=!1}add(t){tr(this.children,t),this.isDirty=!0}remove(t){tn(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nT),this.isDirty=!1,this.children.forEach(t)}}function nE(t){let e=tu(t)?t.get():t;return Y(e)?e.toValue():e}let nA=["TopLeft","TopRight","BottomLeft","BottomRight"],nP=nA.length,nC=t=>"string"==typeof t?parseFloat(t):t,nk=t=>"number"==typeof t||tz.test(t);function nR(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let n_=nO(0,.5,tS),nM=nO(.5,.95,U);function nO(t,e,r){return n=>n<t?0:n>e?1:r(E(t,e,n))}function nD(t,e){t.min=e.min,t.max=e.max}function nj(t,e){nD(t.x,e.x),nD(t.y,e.y)}function nL(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nI(t,e,r,n,i){return t-=e,t=n+1/r*(t-n),void 0!==i&&(t=n+1/i*(t-n)),t}function nV(t,e,[r,n,i],o,s){!function(t,e=0,r=1,n=.5,i,o=t,s=t){if(tU.test(e)&&(e=parseFloat(e),e=e_(s.min,s.max,e/100)-s.min),"number"!=typeof e)return;let a=e_(o.min,o.max,n);t===o&&(a-=e),t.min=nI(t.min,e,r,a,i),t.max=nI(t.max,e,r,a,i)}(t,e[r],e[n],e[i],e.scale,o,s)}let nB=["x","scaleX","originX"],nF=["y","scaleY","originY"];function nN(t,e,r,n){nV(t.x,e,nB,r?r.x:void 0,n?n.x:void 0),nV(t.y,e,nF,r?r.y:void 0,n?n.y:void 0)}function nU(t){return 0===t.translate&&1===t.scale}function nz(t){return nU(t.x)&&nU(t.y)}function nW(t,e){return t.min===e.min&&t.max===e.max}function nH(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nY(t,e){return nH(t.x,e.x)&&nH(t.y,e.y)}function nX(t){return rL(t.x)/rL(t.y)}function n$(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nq{constructor(){this.members=[]}add(t){tr(this.members,t),t.scheduleRender()}remove(t){if(tn(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nG={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},nK="undefined"!=typeof window&&void 0!==window.MotionDebug,nZ=["","X","Y","Z"],nJ={visibility:"hidden"},nQ=0;function n0(t,e,r,n){let{latestValues:i}=e;i[t]&&(r[t]=i[t],e.setStaticValue(t,0),n&&(n[t]=0))}function n1({attachResizeListener:t,defaultParent:e,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(t={},r=null==e?void 0:e()){this.id=nQ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nK&&(nG.totalNodes=nG.resolvedTargetDeltas=nG.recalculatedProjection=0),this.nodes.forEach(n3),this.nodes.forEach(ie),this.nodes.forEach(ir),this.nodes.forEach(n6),nK&&window.MotionDebug.record(nG)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nS)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ti),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let r=this.eventHandlers.get(t);r&&r.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:n,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(i||n)&&(this.isLayoutDirty=!0),t){let r;let n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=te.now(),n=({timestamp:e})=>{let i=e-r;i>=250&&(Z(n),t(i-250))};return K.read(n,!0),()=>Z(n)}(n,0),nf.hasAnimatedSinceResize&&(nf.hasAnimatedSinceResize=!1,this.nodes.forEach(it))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&o&&(n||i)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||o.getDefaultTransition()||iu,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!nY(this.targetLayout,n)||r,u=!e&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...v(i,"layout"),onPlay:s,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||it(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ii),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:r}=e.options;if(!r)return;let n=r.props[td];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:r}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",K,!(t||r))}let{parent:i}=e;i&&!i.hasCheckedOptimisedAppear&&t(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n4);return}this.isUpdating||this.nodes.forEach(n9),this.isUpdating=!1,this.nodes.forEach(n7),this.nodes.forEach(n2),this.nodes.forEach(n5),this.clearAllSnapshots();let t=te.now();J.delta=tP(0,1e3/60,t-J.timestamp),J.timestamp=t,J.isProcessing=!0,Q.update.process(J),Q.preRender.process(J),Q.render.process(J),J.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,nv.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n8),this.sharedNodes.forEach(io)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rq(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!i)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nz(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;t&&(e||rQ(this.latestValues)||o)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let r=this.measurePageBox(),n=this.removeElementScroll(r);return t&&(n=this.removeTransform(n)),id((e=n).x),id(e.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return rq();let r=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(im))){let{scroll:t}=this.root;t&&(r3(r.x,t.offset.x),r3(r.y,t.offset.y))}return r}removeElementScroll(t){var e;let r=rq();if(nj(r,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return r;for(let e=0;e<this.path.length;e++){let n=this.path[e],{scroll:i,options:o}=n;n!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&nj(r,t),r3(r.x,i.offset.x),r3(r.y,i.offset.y))}return r}applyTransform(t,e=!1){let r=rq();nj(r,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&r8(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),rQ(n.latestValues)&&r8(r,n.latestValues)}return rQ(this.latestValues)&&r8(r,this.latestValues),r}removeTransform(t){let e=rq();nj(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!rQ(r.latestValues))continue;rJ(r.latestValues)&&r.updateSnapshot();let n=rq();nj(n,r.measurePageBox()),nN(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return rQ(this.latestValues)&&nN(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==J.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,r,n,i;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==o;if(!(t||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=J.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rq(),this.relativeTargetOrigin=rq(),rN(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nj(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=rq(),this.targetWithTransforms=rq()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,n=this.relativeTarget,i=this.relativeParent.target,rB(r.x,n.x,i.x),rB(r.y,n.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nj(this.target,this.layout.layoutBox),r5(this.target,this.targetDelta)):nj(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rq(),this.relativeTargetOrigin=rq(),rN(this.relativeTargetOrigin,this.target,t.target),nj(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nK&&nG.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||rJ(this.parent.latestValues)||r0(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),r=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===J.timestamp&&(n=!1),n)return;let{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||o))return;nj(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(t,e,r,n=!1){let i,o;let s=r.length;if(s){e.x=e.y=1;for(let a=0;a<s;a++){o=(i=r[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&r8(t,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,r5(t,o)),n&&rQ(i.latestValues)&&r8(t,i.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,r),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=rq());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nL(this.prevProjectionDelta.x,this.projectionDelta.x),nL(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rV(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&n$(this.projectionDelta.x,this.prevProjectionDelta.x)&&n$(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nK&&nG.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rX(),this.projectionDelta=rX(),this.projectionDeltaWithTransform=rX()}setAnimationOrigin(t,e=!1){let r;let n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},s=rX();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=rq(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(il));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(is(s.x,t.x,n),is(s.y,t.y,n),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p;rN(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,ia(f.x,p.x,a.x,n),ia(f.y,p.y,a.y,n),r&&(u=this.relativeTarget,d=r,nW(u.x,d.x)&&nW(u.y,d.y))&&(this.isProjectionDirty=!1),r||(r=rq()),nj(r,this.relativeTarget)}l&&(this.animationValues=o,function(t,e,r,n,i,o){i?(t.opacity=e_(0,void 0!==r.opacity?r.opacity:1,n_(n)),t.opacityExit=e_(void 0!==e.opacity?e.opacity:1,0,nM(n))):o&&(t.opacity=e_(void 0!==e.opacity?e.opacity:1,void 0!==r.opacity?r.opacity:1,n));for(let i=0;i<nP;i++){let o=`border${nA[i]}Radius`,s=nR(e,o),a=nR(r,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||nk(s)===nk(a)?(t[o]=Math.max(e_(nC(s),nC(a),n),0),(tU.test(a)||tU.test(s))&&(t[o]+="%")):t[o]=a)}(e.rotate||r.rotate)&&(t.rotate=e_(e.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{nf.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,r){let n=tu(0)?0:tl(0);return n.start(rh("",n,1e3,r)),n.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:n,latestValues:i}=t;if(e&&r&&n){if(this!==t&&this.layout&&n&&ip(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rq();let e=rL(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let n=rL(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+n}nj(e,r),r8(e,i),rV(this.projectionDeltaWithTransform,this.layoutCorrected,e,i)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nq),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(e=!0),!e)return;let n={};r.z&&n0("z",t,n,this.animationValues);for(let e=0;e<nZ.length;e++)n0(`rotate${nZ[e]}`,t,n,this.animationValues),n0(`skew${nZ[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){var e,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nJ;let n={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=nE(null==t?void 0:t.pointerEvents)||"",n.transform=i?i(this.latestValues,""):"none",n;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nE(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!rQ(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),n.transform=function(t,e,r){let n="",i=t.x.translate/e.x,o=t.y.translate/e.y,s=(null==r?void 0:r.z)||0;if((i||o||s)&&(n=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),r){let{transformPerspective:t,rotate:e,rotateX:i,rotateY:o,skewX:s,skewY:a}=r;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),i&&(n+=`rotateX(${i}deg) `),o&&(n+=`rotateY(${o}deg) `),s&&(n+=`skewX(${s}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(n.transform=i(s,n.transform));let{x:a,y:l}=this.projectionDelta;for(let t in n.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?n.opacity=o===this?null!==(r=null!==(e=s.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:n.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,ng){if(void 0===s[t])continue;let{correct:e,applyTo:r}=ng[t],i="none"===n.transform?s[t]:e(s[t],o);if(r){let t=r.length;for(let e=0;e<t;e++)n[r[e]]=i}else n[t]=i}return this.options.layoutId&&(n.pointerEvents=o===this?nE(null==t?void 0:t.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(n4),this.root.sharedNodes.clear()}}}function n2(t){t.updateLayout()}function n5(t){var e;let r=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&r&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:i}=t.options,o=r.source!==t.layout.source;"size"===i?rG(t=>{let n=o?r.measuredBox[t]:r.layoutBox[t],i=rL(n);n.min=e[t].min,n.max=n.min+i}):ip(i,r.layoutBox,e)&&rG(n=>{let i=o?r.measuredBox[n]:r.layoutBox[n],s=rL(e[n]);i.max=i.min+s,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+s)});let s=rX();rV(s,e,r.layoutBox);let a=rX();o?rV(a,t.applyTransform(n,!0),r.measuredBox):rV(a,e,r.layoutBox);let l=!nz(s),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let s=rq();rN(s,r.layoutBox,i.layoutBox);let a=rq();rN(a,e,o.layoutBox),nY(s,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=s,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:r,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function n3(t){nK&&nG.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function n6(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function n8(t){t.clearSnapshot()}function n4(t){t.clearMeasurements()}function n9(t){t.isLayoutDirty=!1}function n7(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function it(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ie(t){t.resolveTargetDelta()}function ir(t){t.calcProjection()}function ii(t){t.resetSkewAndRotation()}function io(t){t.removeLeadSnapshot()}function is(t,e,r){t.translate=e_(e.translate,0,r),t.scale=e_(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function ia(t,e,r,n){t.min=e_(e.min,r.min,n),t.max=e_(e.max,r.max,n)}function il(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let iu={duration:.45,ease:[.4,0,.1,1]},ic=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ih=ic("applewebkit/")&&!ic("chrome/")?Math.round:U;function id(t){t.min=ih(t.min),t.max=ih(t.max)}function ip(t,e,r){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nX(e)-nX(r)))}function im(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}let ig=n1({attachResizeListener:(t,e)=>rE(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),iv={current:void 0},iy=n1({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!iv.current){let t=new ig({});t.mount(window),t.setOptions({layoutScroll:!0}),iv.current=t}return iv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ib(t,e,r){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&K.postRender(()=>i(e,rA(e)))}class ix extends rx{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[n,i,o]=R(t,r),s=_(t=>{let{target:r}=t,n=e(t);if("function"!=typeof n||!r)return;let o=_(t=>{n(t),r.removeEventListener("pointerleave",o)});r.addEventListener("pointerleave",o,i)});return n.forEach(t=>{t.addEventListener("pointerenter",s,i)}),o}(t,t=>(ib(this.node,t,"Start"),t=>ib(this.node,t,"End"))))}unmount(){}}class iw extends rx{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eF(rE(this.node.current,"focus",()=>this.onFocus()),rE(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function iT(t,e,r){let{props:n}=t;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&K.postRender(()=>i(e,rA(e)))}class iS extends rx{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[n,i,o]=R(t,r),s=t=>{let n=t.currentTarget;if(!B(t)||j.has(n))return;j.add(n);let o=e(t),s=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),B(t)&&j.has(n)&&(j.delete(n),"function"==typeof o&&o(t,{success:e}))},a=t=>{s(t,r.useGlobalTarget||M(n,t.target))},l=t=>{s(t,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return n.forEach(t=>{D.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(r.useGlobalTarget?window:t).addEventListener("pointerdown",s,i),t.addEventListener("focus",t=>V(t,i),i)}),o}(t,t=>(iT(this.node,t,"Start"),(t,{success:e})=>iT(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iE=new WeakMap,iA=new WeakMap,iP=t=>{let e=iE.get(t.target);e&&e(t)},iC=t=>{t.forEach(iP)},ik={some:0,all:1};class iR extends rx{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:n="some",once:i}=t,o={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:ik[n]};return function(t,e,r){let n=function({root:t,...e}){let r=t||document;iA.has(r)||iA.set(r,{});let n=iA.get(r),i=JSON.stringify(e);return n[i]||(n[i]=new IntersectionObserver(iC,{root:t,...e})),n[i]}(e);return iE.set(t,r),n.observe(t),()=>{iE.delete(t),n.unobserve(t)}}(this.node.current,o,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,i&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=e?r:n;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return r=>t[r]!==e[r]}(t,e))&&this.startObserver()}unmount(){}}let i_=(0,nu.createContext)({strict:!1});var iM=r(9791);let iO=(0,nu.createContext)({});function iD(t){return i(t.animate)||d.some(e=>a(t[e]))}function ij(t){return!!(iD(t)||t.variants)}function iL(t){return Array.isArray(t)?t.join(" "):t}var iI=r(7282);let iV={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iB={};for(let t in iV)iB[t]={isEnabled:e=>iV[t].some(t=>!!e[t])};let iF=Symbol.for("motionComponentSymbol");var iN=r(7797),iU=r(9033);let iz=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function iW(t){if("string"!=typeof t||t.includes("-"));else if(iz.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var iH=r(458);let iY=t=>(e,r)=>{let n=(0,nu.useContext)(iO),o=(0,nu.useContext)(iN.O),s=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:r},n,o,s){let a={latestValues:function(t,e,r,n){let o={},s=n(t,{});for(let t in s)o[t]=nE(s[t]);let{initial:a,animate:l}=t,c=iD(t),h=ij(t);e&&h&&!c&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let d=!!r&&!1===r.initial,f=(d=d||!1===a)?l:a;if(f&&"boolean"!=typeof f&&!i(f)){let e=Array.isArray(f)?f:[f];for(let r=0;r<e.length;r++){let n=u(t,e[r]);if(n){let{transitionEnd:t,transition:e,...r}=n;for(let t in r){let e=r[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(n,o,s,t),renderState:e()};return r&&(a.onMount=t=>r({props:n,current:t,...a}),a.onUpdate=t=>r(t)),a})(t,e,n,o);return r?s():(0,iH.h)(s)},iX=(t,e)=>e&&"number"==typeof t?e.transform(t):t,i$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iq=z.length;function iG(t,e,r){let{style:n,vars:i,transformOrigin:o}=t,s=!1,a=!1;for(let t in e){let r=e[t];if(W.has(t)){s=!0;continue}if(ev(t)){i[t]=r;continue}{let e=iX(r,t9[t]);t.startsWith("origin")?(a=!0,o[t]=e):n[t]=e}}if(!e.transform&&(s||r?n.transform=function(t,e,r){let n="",i=!0;for(let o=0;o<iq;o++){let s=z[o],a=t[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===(s.startsWith("scale")?1:0):0===parseFloat(a))||r){let t=iX(a,t9[s]);if(!l){i=!1;let e=i$[s]||s;n+=`${e}(${t}) `}r&&(e[s]=t)}}return n=n.trim(),r?n=r(e,i?"":n):i&&(n="none"),n}(e,t.transform,r):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:r=0}=o;n.transformOrigin=`${t} ${e} ${r}`}}let iK={offset:"stroke-dashoffset",array:"stroke-dasharray"},iZ={offset:"strokeDashoffset",array:"strokeDasharray"};function iJ(t,e,r){return"string"==typeof t?t:tz.transform(e+r*t)}function iQ(t,{attrX:e,attrY:r,attrScale:n,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(iG(t,u,h),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:f,dimensions:p}=t;d.transform&&(p&&(f.transform=d.transform),delete d.transform),p&&(void 0!==i||void 0!==o||f.transform)&&(f.transformOrigin=function(t,e,r){let n=iJ(e,t.x,t.width),i=iJ(r,t.y,t.height);return`${n} ${i}`}(p,void 0!==i?i:.5,void 0!==o?o:.5)),void 0!==e&&(d.x=e),void 0!==r&&(d.y=r),void 0!==n&&(d.scale=n),void 0!==s&&function(t,e,r=1,n=0,i=!0){t.pathLength=1;let o=i?iK:iZ;t[o.offset]=tz.transform(-n);let s=tz.transform(e),a=tz.transform(r);t[o.array]=`${s} ${a}`}(d,s,a,l,!1)}let i0=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),i1=()=>({...i0(),attrs:{}}),i2=t=>"string"==typeof t&&"svg"===t.toLowerCase();function i5(t,{style:e,vars:r},n,i){for(let o in Object.assign(t.style,e,i&&i.getProjectionStyles(n)),r)t.style.setProperty(o,r[o])}let i3=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function i6(t,e,r,n){for(let r in i5(t,e,void 0,n),e.attrs)t.setAttribute(i3.has(r)?r:th(r),e.attrs[r])}function i8(t,{layout:e,layoutId:r}){return W.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!ng[t]||"opacity"===t)}function i4(t,e,r){var n;let{style:i}=t,o={};for(let s in i)(tu(i[s])||e.style&&tu(e.style[s])||i8(s,t)||(null===(n=null==r?void 0:r.getValue(s))||void 0===n?void 0:n.liveStyle)!==void 0)&&(o[s]=i[s]);return o}function i9(t,e,r){let n=i4(t,e,r);for(let r in t)(tu(t[r])||tu(e[r]))&&(n[-1!==z.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]);return n}let i7=["x","y","width","height","cx","cy","r"],ot={useVisualState:iY({scrapeMotionValuesFromProps:i9,createRenderState:i1,onUpdate:({props:t,prevProps:e,current:r,renderState:n,latestValues:i})=>{if(!r)return;let o=!!t.drag;if(!o){for(let t in i)if(W.has(t)){o=!0;break}}if(!o)return;let s=!e;if(e)for(let r=0;r<i7.length;r++){let n=i7[r];t[n]!==e[n]&&(s=!0)}s&&K.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(r,n),K.render(()=>{iQ(n,i,i2(r.tagName),t.transformTemplate),i6(r,n)})})}})},oe={useVisualState:iY({scrapeMotionValuesFromProps:i4,createRenderState:i0})};function or(t,e,r){for(let n in e)tu(e[n])||i8(n,r)||(t[n]=e[n])}let on=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function oi(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||on.has(t)}let oo=t=>!oi(t);try{(no=require("@emotion/is-prop-valid").default)&&(oo=t=>t.startsWith("on")?!oi(t):no(t))}catch(t){}let os={current:null},oa={current:!1},ol=[...eS,t$,t2],ou=t=>ol.find(eT(t)),oc=new WeakMap,oh=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class od{scrapeMotionValuesFromProps(t,e,r){return{}}constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ep,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=te.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,K.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=iD(e),this.isVariantNode=ij(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:c,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&tu(e)&&e.set(a[t],!1)}}mount(t){this.current=t,oc.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),oa.current||function(){if(oa.current=!0,iI.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>os.current=t.matches;t.addListener(e),e()}else os.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||os.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in oc.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let r;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=W.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&K.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),o(),r&&r(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in iB){let e=iB[t];if(!e)continue;let{isEnabled:r,Feature:n}=e;if(!this.features[t]&&n&&r(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rq()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<oh.length;e++){let r=oh[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=t["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(t,e,r){for(let n in e){let i=e[n],o=r[n];if(tu(i))t.addValue(n,i);else if(tu(o))t.addValue(n,tl(i,{owner:t}));else if(o!==i){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{let e=t.getStaticValue(n);t.addValue(n,tl(void 0!==e?e:i,{owner:t}))}}}for(let n in r)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let r=this.values.get(t);e!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=tl(null===e?void 0:e,{owner:this}),this.addValue(t,r)),r}readValue(t,e){var r;let n=void 0===this.latestValues[t]&&this.current?null!==(r=this.getBaseTargetFromProps(this.props,t))&&void 0!==r?r:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(em(n)||tA(n))?n=parseFloat(n):!ou(n)&&t2.test(e)&&(n=ee(t,e)),this.setBaseTarget(t,tu(n)?n.get():n)),tu(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let r;let{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let i=u(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);i&&(r=i[t])}if(n&&void 0!==r)return r;let i=this.getBaseTargetFromProps(this.props,t);return void 0===i||tu(i)?void 0!==this.initialValues[t]&&void 0===r?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new ti),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class of extends od{constructor(){super(...arguments),this.KeyframeResolver=eA}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:r}){delete e[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tu(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class op extends of{constructor(){super(...arguments),this.type="html",this.renderInstance=i5}readValueFromInstance(t,e){if(W.has(e)){let t=et(e);return t&&t.default||0}{let r=window.getComputedStyle(t),n=(ev(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return r4(t,e)}build(t,e,r){iG(t,e,r.transformTemplate)}scrapeMotionValuesFromProps(t,e,r){return i4(t,e,r)}}class om extends of{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rq}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(W.has(e)){let t=et(e);return t&&t.default||0}return e=i3.has(e)?e:th(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,r){return i9(t,e,r)}build(t,e,r){iQ(t,e,this.isSVGTag,r.transformTemplate)}renderInstance(t,e,r,n){i6(t,e,r,n)}mount(t){this.isSVGTag=i2(t.tagName),super.mount(t)}}let og=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(r,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((ns={animation:{Feature:rw},exit:{Feature:rS},inView:{Feature:iR},tap:{Feature:iS},focus:{Feature:iw},hover:{Feature:ix},pan:{Feature:ni},drag:{Feature:nr,ProjectionNode:iy,MeasureLayout:nx},layout:{ProjectionNode:iy,MeasureLayout:nx}},na=(t,e)=>iW(t)?new om(e):new op(e,{allowProjection:t!==nu.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,r;let{preloadedFeatures:n,createVisualElement:i,useRender:o,useVisualState:s,Component:l}=t;function u(t,e){var r;let n;let u={...(0,nu.useContext)(iM._),...t,layoutId:function(t){let{layoutId:e}=t,r=(0,nu.useContext)(nh.p).id;return r&&void 0!==e?r+"-"+e:e}(t)},{isStatic:c}=u,h=function(t){let{initial:e,animate:r}=function(t,e){if(iD(t)){let{initial:e,animate:r}=t;return{initial:!1===e||a(e)?e:void 0,animate:a(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,nu.useContext)(iO));return(0,nu.useMemo)(()=>({initial:e,animate:r}),[iL(e),iL(r)])}(t),d=s(t,c);if(!c&&iI.j){(0,nu.useContext)(i_).strict;let t=function(t){let{drag:e,layout:r}=iB;if(!e&&!r)return{};let n={...e,...r};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==r?void 0:r.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(u);n=t.MeasureLayout,h.visualElement=function(t,e,r,n,i){var o,s;let{visualElement:a}=(0,nu.useContext)(iO),l=(0,nu.useContext)(i_),u=(0,nu.useContext)(iN.O),c=(0,nu.useContext)(iM._).reducedMotion,h=(0,nu.useRef)(null);n=n||l.renderer,!h.current&&n&&(h.current=n(t,{visualState:e,parent:a,props:r,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:c}));let d=h.current,f=(0,nu.useContext)(nd);d&&!d.projection&&i&&("html"===d.type||"svg"===d.type)&&function(t,e,r,n){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new r(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&rj(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:u})}(h.current,r,i,f);let p=(0,nu.useRef)(!1);(0,nu.useInsertionEffect)(()=>{d&&p.current&&d.update(r,u)});let m=r[td],g=(0,nu.useRef)(!!m&&!(null===(o=window.MotionHandoffIsComplete)||void 0===o?void 0:o.call(window,m))&&(null===(s=window.MotionHasOptimisedAnimation)||void 0===s?void 0:s.call(window,m)));return(0,iU.L)(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),nv.render(d.render),g.current&&d.animationState&&d.animationState.animateChanges())}),(0,nu.useEffect)(()=>{d&&(!g.current&&d.animationState&&d.animationState.animateChanges(),g.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,m)}),g.current=!1))}),d}(l,d,u,i,t.ProjectionNode)}return(0,nl.jsxs)(iO.Provider,{value:h,children:[n&&h.visualElement?(0,nl.jsx)(n,{visualElement:h.visualElement,...u}):null,o(l,t,(r=h.visualElement,(0,nu.useCallback)(t=>{t&&d.onMount&&d.onMount(t),r&&(t?r.mount(t):r.unmount()),e&&("function"==typeof e?e(t):rj(e)&&(e.current=t))},[r])),d,c,h.visualElement)]})}n&&function(t){for(let e in t)iB[e]={...iB[e],...t[e]}}(n),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!==(r=null!==(e=l.displayName)&&void 0!==e?e:l.name)&&void 0!==r?r:"",")"));let c=(0,nu.forwardRef)(u);return c[iF]=l,c}({...iW(t)?ot:oe,preloadedFeatures:ns,useRender:function(t=!1){return(e,r,n,{latestValues:i},o)=>{let s=(iW(e)?function(t,e,r,n){let i=(0,nu.useMemo)(()=>{let r=i1();return iQ(r,e,i2(n),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};or(e,t.style,t),i.style={...e,...i.style}}return i}:function(t,e){let r={},n=function(t,e){let r=t.style||{},n={};return or(n,r,t),Object.assign(n,function({transformTemplate:t},e){return(0,nu.useMemo)(()=>{let r=i0();return iG(r,e,t),Object.assign({},r.vars,r.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,o,e),a=function(t,e,r){let n={};for(let i in t)("values"!==i||"object"!=typeof t.values)&&(oo(i)||!0===r&&oi(i)||!e&&!oi(i)||t.draggable&&i.startsWith("onDrag"))&&(n[i]=t[i]);return n}(r,"string"==typeof e,t),l=e!==nu.Fragment?{...a,...s,ref:n}:{},{children:u}=r,c=(0,nu.useMemo)(()=>tu(u)?u.get():u,[u]);return(0,nu.createElement)(e,{...l,children:c})}}(e),createVisualElement:na,Component:t})}))},7282:function(t,e,r){"use strict";r.d(e,{j:function(){return n}});let n="undefined"!=typeof window},458:function(t,e,r){"use strict";r.d(e,{h:function(){return i}});var n=r(2265);function i(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},9033:function(t,e,r){"use strict";r.d(e,{L:function(){return i}});var n=r(2265);let i=r(7282).j?n.useLayoutEffect:n.useEffect},3463:function(t,e,r){"use strict";var n=r(753);e.ZP=n.default||n}}]);