#!/bin/bash

# Alpago Next.js Deployment Script
echo "🚀 Starting Alpago deployment..."

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the application
echo "🔨 Building the application..."
npm run build

# Create logs directory if it doesn't exist
mkdir -p logs

# Stop existing PM2 processes
echo "🛑 Stopping existing PM2 processes..."
npx pm2 stop alpago 2>/dev/null || echo "No existing processes to stop"

# Start the application with PM2 in production mode
echo "🚀 Starting application with PM2 cluster mode..."
npm run pm2:start -- --env production

# Save PM2 process list
echo "💾 Saving PM2 process list..."
npx pm2 save

# Show status
echo "📊 Current PM2 status:"
npx pm2 status

echo "✅ Deployment completed!"
echo "🌐 Application is running on http://localhost:3006"
echo "📝 Use 'npm run pm2:logs' to view logs"
echo "📊 Use 'npx pm2 status' to check status"
echo "🔄 Use 'npm run pm2:restart' to restart"
echo "🛑 Use 'npm run pm2:stop' to stop"
