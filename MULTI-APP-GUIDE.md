# Multi-Application Server Management Guide

## Overview
This guide explains how to manage multiple applications on the same server using PM2, with proper resource allocation and conflict prevention.

## Current Optimized Configuration

### Alpago Application
- **Instances**: 4 (optimized for multi-app environment)
- **Memory Limit**: 512MB per instance
- **Port**: 3006
- **Namespace**: alpago-app
- **Total Memory Usage**: ~2GB

## Key Considerations for Multi-App Environment

### 1. Resource Management
```bash
# Check system resources
./multi-app-manager.sh status
```

**CPU Allocation:**
- Total CPU cores are shared among all applications
- Recommended: 2-4 instances per app (instead of 'max')
- Monitor CPU usage: `npx pm2 monit`

**Memory Management:**
- Set memory limits for each app: `max_memory_restart: '512M'`
- Monitor memory usage to prevent OOM kills
- Use `node_args: '--max-old-space-size=512'` to limit Node.js heap

### 2. Port Management
```bash
# Check port usage
./multi-app-manager.sh status
```

**Port Allocation Strategy:**
- App 1: 3000-3009 (development), 3010-3019 (production)
- App 2: 3020-3029 (development), 3030-3039 (production)
- API Services: 4000-4099
- Admin Panels: 5000-5099
- Workers/Background: 6000-6099

### 3. Namespace Organization
Use namespaces to group related processes:
```javascript
namespace: 'app-name'  // Groups processes logically
```

## Multi-Application Configuration Template

### Example ecosystem.config.js for each app:
```javascript
module.exports = {
  apps: [{
    name: 'your-app-name',
    script: 'server.js',
    instances: process.env.PM2_INSTANCES || 2,
    exec_mode: 'cluster',
    max_memory_restart: '512M',
    node_args: '--max-old-space-size=512',
    namespace: 'your-app-namespace',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3010  // Unique port
    }
  }]
};
```

## Management Commands

### System Overview
```bash
# Complete system status
./multi-app-manager.sh status

# Real-time monitoring
./multi-app-manager.sh monitor

# View all logs
./multi-app-manager.sh logs
```

### Application Management
```bash
# Restart with optimization
./multi-app-manager.sh restart app-name

# Optimize all applications
./multi-app-manager.sh optimize

# Standard PM2 commands
npx pm2 list
npx pm2 restart app-name
npx pm2 stop app-name
npx pm2 delete app-name
```

## Best Practices

### 1. Resource Allocation
- **High-traffic apps**: 3-4 instances
- **Medium-traffic apps**: 2 instances  
- **Low-traffic/admin apps**: 1 instance
- **Background workers**: 1 instance (fork mode)

### 2. Memory Limits
- **Frontend apps**: 512MB per instance
- **API services**: 256-512MB per instance
- **Admin panels**: 256MB per instance
- **Workers**: 128-256MB per instance

### 3. Monitoring
```bash
# Check resource usage
npx pm2 monit

# View specific app logs
npx pm2 logs app-name --lines 100

# Check system resources
htop  # or top on macOS
```

### 4. Load Balancing
- Use nginx/apache as reverse proxy
- Configure upstream servers for each app
- Implement health checks

## Example Nginx Configuration
```nginx
upstream alpago_backend {
    server 127.0.0.1:3006;
}

upstream api_backend {
    server 127.0.0.1:4001;
}

server {
    listen 80;
    server_name alpago-dev-new.e8demo.com;
    
    location / {
        proxy_pass http://alpago_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

server {
    listen 80;
    server_name api.alpago-dev-new.e8demo.com;
    
    location / {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Troubleshooting

### High Memory Usage
```bash
# Check memory per app
npx pm2 show app-name

# Restart with lower memory limit
PM2_INSTANCES=2 npx pm2 restart app-name
```

### Port Conflicts
```bash
# Check what's using a port
lsof -i :3006

# Kill process on port
kill -9 $(lsof -t -i:3006)
```

### Performance Issues
```bash
# Monitor real-time
npx pm2 monit

# Check logs for errors
npx pm2 logs app-name --err

# Restart problematic app
npx pm2 restart app-name
```

## Cluster Impact on Other Applications

### Positive Impacts:
- **Load Distribution**: Each app gets dedicated CPU cores
- **Fault Tolerance**: If one instance fails, others continue
- **Better Performance**: Parallel processing

### Potential Issues:
- **Memory Usage**: More instances = more memory
- **CPU Competition**: All apps compete for CPU time
- **Port Management**: Need careful port allocation

### Mitigation Strategies:
1. **Limit Instances**: Use 2-4 instances instead of 'max'
2. **Set Memory Limits**: Prevent any app from consuming all memory
3. **Use Namespaces**: Organize and monitor apps separately
4. **Monitor Resources**: Regular monitoring and optimization
5. **Stagger Restarts**: Don't restart all apps simultaneously
